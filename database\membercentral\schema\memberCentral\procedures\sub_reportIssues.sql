ALTER PROC dbo.sub_reportIssues
@siteID int,
@sendemail bit,
@finalMSG varchar(max) OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	declare @orgID int, @subscriptionIssuesEmail varchar(max), @checkID int, @dynSQL VARCHAR(max), 
		@errorSubject VARCHAR(400), @sitename varchar(60), @orgSysMemberID int, @messageTypeID int, 
		@sendingSiteResourceID int, @tier VARCHAR(12), @messageID INT, @recipientIDList VARCHAR(max),
		@siteCode VARCHAR(10), @hostName varchar(200), @memberEditJumpToToolParams varchar(200);

	SELECT @orgID = orgID, @sitename = sitename, @siteCode = siteCode, @subscriptionIssuesEmail = subscriptionIssuesEmail 
	FROM dbo.sites 
	WHERE siteID = @siteID;

	if object_id('tempdb..#tmpEmailIssuesReport') is not null 
		drop table #tmpEmailIssuesReport;
	create table #tmpEmailIssuesReport (autoID int identity(1,1), checkID tinyint, checkContent varchar(max));


	-- Active Subscription Rates with non-active GL Accounts
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select 1, concat('<li>Rate Schedule: ',rs.scheduleName,'; Rate: ',r.rateName,'; GL Account: ',gl.accountName,'</li>')
	from dbo.tr_GLAccounts as gl
	inner join dbo.sub_rates as r on r.GLAccountID = gl.GLAccountID
		and gl.[status] <> 'A'
		and r.[status] = 'A'
	inner join dbo.sub_rateSchedules rs on rs.siteID = @siteID and rs.scheduleID = r.scheduleID
		and rs.[status] = 'A'
	where gl.orgID = @orgID;


	-- Active Subscriptions with non-active GL Accounts
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select 2, concat('<li>Subscription Type: ',t.typeName,'; Subscription: ',subs.subscriptionName,'; GL Account: ',gl.accountName,'</li>')
	from dbo.tr_GLAccounts as gl
	inner join dbo.sub_subscriptions as subs on subs.orgID = @orgID and subs.GLAccountID = gl.GLAccountID
		and gl.[status] <> 'A'
		and subs.[status] = 'A'
	inner join dbo.sub_types t on t.siteID = @siteID and t.typeID = subs.typeID
	where gl.orgID = @orgID;


	-- Renewal Not Sent and Offered Subscribers with non-active GL Accounts
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select 3, concat('<li>Subscription: ',subscriptionName,'; GL Account: ',AccountName,'; Number of Offers: ',numOffers,'</li>')
	from (
		select gl.GLAccountID, gl.AccountName, subs.subscriptionName, count(*) as numOffers
		from dbo.tr_GLAccounts as gl
		inner join dbo.sub_subscribers ss on ss.orgID = @orgID and ss.GLAccountID = gl.GLAccountID
			and gl.[status] <> 'A'
		inner join dbo.sub_statuses as st on st.statusID = ss.statusID
			and st.statusCode in ('R','O')
		inner join dbo.sub_subscriptions as subs on subs.orgID = @orgID and subs.subscriptionID = ss.subscriptionID
		where gl.orgID = @orgID
		group by gl.GLAccountID, gl.AccountName, subs.subscriptionName
	) as tmp;


	-- Subscription Rate Frequencies with no Pay Profile
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select 4, concat('<li>Rate Schedule: ',scheduleName,'; Rate: ',rateName,'; Frequency: ',frequencyName,'; Subscriptions: ',replace(affectedSubscriptions,'|',', '),'</li>')
	from (
		select rs.schedulename, r.ratename, f.frequencyname, STRING_AGG(cast(subs.subscriptionname as varchar(max)),'|') WITHIN GROUP (ORDER BY subs.subscriptionname ASC) as affectedSubscriptions
		from dbo.sub_types as t
		inner join dbo.sub_subscriptions as subs on subs.orgID = @orgID and subs.typeID = t.typeID and subs.status = 'A'
		inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = subs.scheduleID and rs.status = 'A'
		inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A'
		inner join dbo.sub_rateFrequencies as rf on rf.rateID = r.rateID and rf.status = 'A'
		inner join dbo.sub_frequencies as f on f.frequencyID = rf.frequencyID
		left outer join dbo.sub_rateFrequenciesMerchantProfiles as rfmp on rfmp.rfid = rf.rfid
		where t.siteID = @siteID
		and t.status = 'A'
		and rfmp.rfid is null
		group by rs.scheduleName, r.rateName, f.frequencyName
	) as tmp;


	-- Subscription Dates Out of Order
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select 5, concat('<li>Rate Schedule: ',scheduleName,'; Rate: ',ratename,'; Subscriptions: ',replace(affectedSubscriptions,'|',', '),'</li>')
	from (
		select rs.schedulename, r.ratename, count(distinct subs.subscriptionname) as affectedSubscriptions
		from dbo.sub_types as t 
		inner join dbo.sub_subscriptions as subs on subs.orgID = @orgID and subs.typeID = t.typeID and subs.status = 'A'
		inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = subs.scheduleID and rs.status = 'A'
		inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' 
		where t.siteID = @siteID
		and t.status = 'A'
		and (r.graceEndDate < r.termAFEndDate or r.termAFEndDate < r.termAFStartDate or r.recogAFEndDate < r.recogAFStartDate or r.rateAFEndDate < r.rateAFStartDate)
		group by rs.scheduleName, r.rateName
	) as tmp;


	-- Subscription Rates Missing Advance Formulas
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select distinct 6, concat('<li>Rate Schedule: ',rs.scheduleName,'; Rate: ',r.ratename,'</li>')
	from dbo.sub_types as t 
	inner join dbo.sub_subscriptions as subs on subs.orgID = @orgID and subs.typeID = t.typeID and subs.status = 'A'
	inner join dbo.sub_rateSchedules as rs on rs.siteID = @siteID and rs.scheduleID = subs.scheduleID and rs.status = 'A'
	inner join dbo.sub_rates as r on r.scheduleID = rs.scheduleID and r.status = 'A' and r.ratename not like ('%Import%')
	and (
		(r.rateStartDateAFID is null and datediff(yy,getdate(),r.rateAFStartDate) < 100) or 
		(r.rateEndDateAFID is null and  datediff(yy,getdate(),r.rateAFEndDate) < 100) or
		(r.termStartDateAFID is null and  datediff(yy,getdate(),r.termAFStartDate) < 100) or
		(r.termEndDateAFID is null and datediff(yy,getdate(),r.termAFEndDate) < 100) or
		(r.graceAFID is null and r.graceEndDate is not null and datediff(yy,getdate(),r.graceEndDate) < 100) or
		(r.recogStartDateAFID is null and  datediff(yy,getdate(),r.recogAFStartDate) < 100) or
		(r.recogEndDateAFID is null and datediff(yy,getdate(),r.recogAFEndDate) < 100)
	)
	where t.siteID = @siteID
	and t.status = 'A';


	-- Deleted Rates In Report Conditions
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select 7, concat('<li>Report: ',sr.reportName,'; Rate: ',r.rateName,'; Filter: ',c.[verbose],'</li>')
	from dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.sub_rates as r on cast(r.rateID as varchar(10)) = cv.conditionValue
	inner join dbo.ams_virtualGroupConditions as c on c.orgID = @orgID and c.conditionID = cv.conditionID and c.conditionTypeID = 2
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subRate'
	outer apply (
		select distinct vgr.ruleID, vgr.ruleName
		from dbo.ams_virtualGroupRules as vgr
		inner join dbo.ams_virtualGroupRuleVersions as vgrv on vgrv.orgID = @orgID
			and vgrv.ruleVersionID = vgr.activeVersionID
		inner join dbo.ams_virtualGroupRuleConditionSets as rcs on rcs.ruleID = vgr.ruleID
			and rcs.ruleVersionID = vgrv.ruleVersionID
		inner join dbo.ams_virtualGroupRuleConditions as rc on rc.conditionSetID = rcs.conditionSetID
		where vgr.orgID = @orgID
		and rc.conditionID = c.conditionID
		and vgr.ruleTypeID = 2
	) as rc
	inner join dbo.rpt_SavedReports as sr on sr.siteID = @siteID and sr.ruleID = rc.ruleID
	where r.[status] = 'D';


	-- Deleted Rates In Email Blast Conditions
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select 8, concat('<li>Email Blast: ',eb.blastName,'; Rate: ',r.rateName,'; Filter: ',c.[verbose],'</li>')
	from dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.sub_rates as r on cast(r.rateID as varchar(10)) = cv.conditionValue
	inner join dbo.ams_virtualGroupConditions as c on c.orgID = @orgID and c.conditionID = cv.conditionID and c.conditionTypeID = 3
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subRate'
	outer apply (
		select distinct vgr.ruleID, vgr.ruleName
		from dbo.ams_virtualGroupRules as vgr
		inner join dbo.ams_virtualGroupRuleVersions as vgrv on vgrv.orgID = @orgID
			and vgrv.ruleVersionID = vgr.activeVersionID
		inner join dbo.ams_virtualGroupRuleConditionSets as rcs on rcs.ruleID = vgr.ruleID
			and rcs.ruleVersionID = vgrv.ruleVersionID
		inner join dbo.ams_virtualGroupRuleConditions as rc on rc.conditionSetID = rcs.conditionSetID
		where vgr.orgID = @orgID
		and rc.conditionID = c.conditionID
		and vgr.ruleTypeID = 3
	) as rc
	inner join dbo.email_EmailBlasts as eb on eb.siteID = @siteID and eb.ruleID = rc.ruleID
	where r.[status] = 'D';


	-- Deleted Rates In Group Conditions
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select 9, concat('<li>Group Assignment Rule: ',rc.ruleName,'; Rate: ',r.rateName,'; Filter: ',c.[verbose],'</li>')
	from dbo.ams_virtualGroupConditionValues as cv
	inner join dbo.sub_rates as r on cast(r.rateID as varchar(10)) = cv.conditionValue
	inner join dbo.ams_virtualGroupConditions as c on c.orgID = @orgID and c.conditionID = cv.conditionID and c.conditionTypeID = 1
	inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'subRate'
	outer apply (
		select distinct vgr.ruleID, vgr.ruleName
		from dbo.ams_virtualGroupRules as vgr
		inner join dbo.ams_virtualGroupRuleVersions as vgrv on vgrv.orgID = @orgID
			and vgrv.ruleVersionID = vgr.activeVersionID
		inner join dbo.ams_virtualGroupRuleConditionSets as rcs on rcs.ruleID = vgr.ruleID
			and rcs.ruleVersionID = vgrv.ruleVersionID
		inner join dbo.ams_virtualGroupRuleConditions as rc on rc.conditionSetID = rcs.conditionSetID
		where vgr.orgID = @orgID
		and rc.conditionID = c.conditionID
		and vgr.ruleTypeID = 1
	) as rc
	where r.[status] = 'D';


	-- Subscription in Subscription Tree Multiple Times
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select 10, concat('<li>Main Subscription: ',subsWithAddOns.subscriptionName,'; Duplicate Subscription Type: ',dupeSubs.typeName,'; Duplicate Subscription: ',dupeSubs.subscriptionName,'</li>')
	from (
		select distinct sub.subscriptionID, sub.subscriptionName
		from dbo.sub_subscriptions as sub
		inner join dbo.sub_addons as ao on ao.subscriptionID = sub.subscriptionID
		where sub.orgID = @orgID 
		and sub.status = 'A' 
		and sub.soldSeparately = 1
	) as subsWithAddOns
	cross apply (
		select sto.subscriptionID, sub.subscriptionName, t.typeName
		from dbo.fn_sub_getSubscriptionTreeOrder(subsWithAddOns.subscriptionID) as sto
		inner join dbo.sub_subscriptions as sub on sub.orgID = @orgID and sub.subscriptionID = sto.subscriptionID
		inner join dbo.sub_types as t on t.siteID = @siteID and t.typeID = sub.typeID
		group by sto.subscriptionID, sub.subscriptionName, t.typeName
		having count(*) > 1
	) as dupeSubs;

	SELECT @hostName = memberCentral.dbo.fn_getSiteHostNameBySiteCode(@sitecode,NULL);
	SET @memberEditJumpToToolParams = 'https://' + @hostName + '/?pg=admin&jumpToTool=MemberAdmin%7Csearch%7Cedit&memberID=';
	-- Duplicate active subscribers
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select 11, concat('<li><a target="_blank" href="',@memberEditJumpToToolParams,memberID,'&tab=subscriptions">',lastname,', ',firstname,' (',membernumber,')</a> - ',subscriptionName,'</li>')
	from (
		select m2.memberID, m2.membernumber, m2.lastname, m2.firstname, s.subscriptionName
		from dbo.sub_subscribers as sub
		inner join dbo.sub_subscriptions as s on s.orgID = @orgID and s.subscriptionID = sub.subscriptionID
		inner join dbo.sub_types as t on t.siteID = @siteID and t.typeID = s.typeID
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = sub.memberID
		inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activememberID
		where sub.orgID = @orgID
		and sub.statusID = 1
		and sub.paymentStatusID = 1
		group by m2.memberid, m2.membernumber, m2.lastname, m2.firstname, s.subscriptionName, s.subscriptionID
		having count(*) > 1
	) as dupeSubs;


	-- Duplicate billed/accepted subscribers
	insert into #tmpEmailIssuesReport (checkID, checkContent)
	select 12, concat('<li><a target="_blank" href="',@memberEditJumpToToolParams,memberID,'&tab=subscriptions">',lastname,', ',firstname,' (',membernumber,')</a> - ',subscriptionName,' (',statusName,')</li>')
	from (
		select m2.memberID, m2.membernumber, m2.lastname, m2.firstname, s.subscriptionName, ss.statusName
		from dbo.sub_subscribers as sub
		inner join dbo.sub_subscriptions as s on s.orgID = @orgID and s.subscriptionID = sub.subscriptionID
		inner join dbo.sub_types as t on t.siteID = @siteID and t.typeID = s.typeID
		inner join dbo.sub_statuses as ss on ss.statusID = sub.statusID
		inner join dbo.ams_members as m on m.orgID = @orgID and m.memberID = sub.memberID
		inner join dbo.ams_members as m2 on m2.orgID = @orgID and m2.memberID = m.activememberID
		where sub.orgID = @orgID
		and sub.statusID IN (4,5)
		group by m2.memberid, m2.membernumber, m2.lastname, m2.firstname, s.subscriptionName, s.subscriptionID, ss.statusName
		having count(*) > 1
	) as dupeSubs;


	set @finalMSG = '';
	select @checkID = min(checkID) from #tmpEmailIssuesReport;
	while @checkID is not null BEGIN
		select @dynSQL = COALESCE(@dynSQL, '') + checkContent
		from #tmpEmailIssuesReport
		where checkID = @checkID;

		if @dynSQL is not null BEGIN
			if @checkID = 1
				set @finalMSG = @finalMSG + 'There are active subscription rates linked to non-active GL Accounts.<ul>' + @dynSQL + '</ul><br/><br/>';
			if @checkID = 2
				set @finalMSG = @finalMSG + 'There are active subscriptions linked to non-active GL Accounts.<ul>' + @dynSQL + '</ul><br/><br/>';
			if @checkID = 3
				set @finalMSG = @finalMSG + 'There are renewal not sent and offered subscribers linked to non-active GL Accounts.<ul>' + @dynSQL + '</ul><br/><br/>';
			if @checkID = 4
				set @finalMSG = @finalMSG + 'There are subscription rate frequencies with no payment profile defined.<ul>' + @dynSQL + '</ul><br/><br/>';
			if @checkID = 5
				set @finalMSG = @finalMSG + 'There are subscriptions with dates out of order -- grace end dates before subscription end dates, availability end dates before start dates, etc. This may be an indicator of missing advance formulas.<ul>' + @dynSQL + '</ul><br/><br/>';
			if @checkID = 6
				set @finalMSG = @finalMSG + 'There are subscriptions with rates missing at least one advance formula.<ul>' + @dynSQL + '</ul><br/><br/>';
			if @checkID = 7
				set @finalMSG = @finalMSG + 'There are report filters that include deleted subscription rates. These filters need to be edited to remove the deleted rates.<ul>' + @dynSQL + '</ul><br/><br/>';
			if @checkID = 8
				set @finalMSG = @finalMSG + 'There are email blast filters that include deleted subscription rates. These filters need to be edited to remove the deleted rates.<ul>' + @dynSQL + '</ul><br/><br/>';
			if @checkID = 9
				set @finalMSG = @finalMSG + 'There are group assignment conditions that include deleted subscription rates. These conditions need to be edited to remove the deleted rates.<ul>' + @dynSQL + '</ul><br/><br/>';
			if @checkID = 10
				set @finalMSG = @finalMSG + 'There are subscriptions that appear more than once in a subscription tree.<ul>' + @dynSQL + '</ul><br/><br/>';
			if @checkID = 11
				set @finalMSG = @finalMSG + 'There are members with multiple active subscriptions of the same status.<ul>' + @dynSQL + '</ul><br/><br/>';
			if @checkID = 12
				set @finalMSG = @finalMSG + 'There are members with multiple billed or accepted subscriptions of the same status.<ul>' + @dynSQL + '</ul><br/><br/>';
		END
			
		set @dynSQL = null;

		select @checkID = min(checkID) from #tmpEmailIssuesReport where checkID > @checkID;
	end

	IF @sendemail = 1 and len(rtrim(ltrim(@finalMSG))) > 0 begin
		SET @finalMSG = '<div>As of ' + convert(varchar(100),getdate(),100) + ' CT the following subscription setup issues exist on your website.</div><br/><br/>' 
			+ @finalMSG + '<div><b>Questions?</b><br/>Contact <NAME_EMAIL> if you have any questions about this report.</div><br/>'
			+ '<div><b>E-mail Removal</b><br/>If you no longer wish to receive these types of emails, you can adjust the recipient list by logging into your '
			+ 'website Control Panel, clicking on the Members tab, then Subscriptions, then Subscriptions Setup and modifying this report''s recipient address '
			+ 'in the "Subscription Settings" tab.</div>';
		
		SELECT @sendingSiteResourceID = siteResourceID from dbo.sites where siteID = @siteID;
		select @tier = tier from membercentral.dbo.fn_getServerSettings();
		select @messageTypeID = messageTypeID from platformMail.dbo.email_messageTypes where messageTypeCode = 'SUPPORTISSUE';
		select @orgSysMemberID = membercentral.dbo.fn_ams_getOrgSystemMemberID(@orgID);
		set @errorSubject = @sitename + ' Subscription Issues Report';

		EXEC platformMail.dbo.email_SendMessage @fromName='MemberCentral', @fromEmail='<EMAIL>', 
			@toEmailList=@subscriptionIssuesEmail, @replyToEmail='', @subject=@errorSubject, 
			@title='Subscription Setup Issues', @messageContent=@finalMSG, 
			@attachmentsList=NULL, @siteID=@siteID, @memberID=@orgSysMemberID, @messageTypeID=@messageTypeID, 
			@sendingSiteResourceID=@sendingSiteResourceID, @referenceType=NULL, @referenceID=NULL, 
			@doWrapEmail=1, @environmentName=@tier, @messageID=@messageID OUTPUT, @recipientIDList=@recipientIDList OUTPUT;
	end

	if object_id('tempdb..#tmpEmailIssuesReport') is not null 
		drop table #tmpEmailIssuesReport;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO
