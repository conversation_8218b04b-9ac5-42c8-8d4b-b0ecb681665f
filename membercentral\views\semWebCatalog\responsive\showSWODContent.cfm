<cfset local.strSeminar = attributes.data.strSeminar>
<cfset local.qryPreReqs = attributes.data.qryPreReqs>
<cfset local.strCredit = attributes.data.strCredit>
<cfset local.JSStructCreditInfo = attributes.data.JSStructCreditInfo>
<cfset local.qryOutline = attributes.data.qryOutline>
<cfset local.qryBundles = attributes.data.qryBundles>
<cfset local.seminarSuggestions = attributes.data.seminarSuggestions>
<cfset local.speakerBio = attributes.data.speakerBio>
<cfset local.qrySWP = attributes.data.qrySWP>
<cfset local.semWeb = attributes.data.semWeb>
<cfset local.isRegOpen = attributes.data.isRegOpen>
<cfset local.preReqFulfilled = attributes.data.preReqFulfilled>
<cfset local.videoPreviewInfo = attributes.data.videoPreviewInfo>
<cfset local.faClassesFiles = attributes.data.faClassesFiles>
<cfset local.seminarID = attributes.data.seminarID>
<cfif isDefined("session.mcstruct.deviceProfile.is_bot") AND session.mcstruct.deviceProfile.is_bot is 1>
	<cfset local.isBot = 1>
<cfelse>
	<cfset local.isBot = 0>
</cfif>

<cfset local.featuredThumbImageFullRootPath = attributes.data.featuredThumbImageFullRootPath>
<cfset local.featuredThumbImageRootPath = attributes.data.featuredThumbImageRootPath>
<cfset local.programFeaturedImagePath = "">
<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.strSeminar.qrySeminar.featureImageID) AND fileExists("#local.featuredThumbImageFullRootPath##local.strSeminar.qrySeminar.featureImageID#-#local.strSeminar.qrySeminar.featureImageSizeID#.#local.strSeminar.qrySeminar.featureImageFileExtension#")>
	<cfset local.programFeaturedImagePath = "#local.featuredThumbImageRootPath##local.strSeminar.qrySeminar.featureImageID#-#local.strSeminar.qrySeminar.featureImageSizeID#.#local.strSeminar.qrySeminar.featureImageFileExtension#">
<cfelseif len(attributes.data.defaultFeaturedImagePathsStr.defaultSWODFeaturedImagePath)>
	<cfset local.programFeaturedImagePath = attributes.data.defaultFeaturedImagePathsStr.defaultSWODFeaturedImagePath>
</cfif>

<cfsavecontent variable="local.onDemandContentJS">
	<cfoutput>
	<cfif NOT local.isBot and len(local.videoPreviewInfo.videoPreviewLink)>
		<script type="text/javascript" src="/assets/common/javascript/videojs/7.7.6/video.min.js"></script>
		<link rel="stylesheet" href="/assets/common/javascript/videojs/7.7.6/video-js.min.css">
	</cfif>
	<script type="text/javascript" src="/assets/common/javascript/swfobject.js"></script>
	<script language="javascript">
		<cfif attributes.data.isRegOpen and NOT local.isBot>
			function enrollNow() { self.location.href='#attributes.event.getValue('mainurl')#&panel=reg&item=SWOD-#local.seminarID#'; }
		</cfif>
		<cfif local.strSeminar.qrySeminar.isRegistered and local.preReqFulfilled and NOT local.isBot>
			function enterSWODProgram() { window.open('/?pg=swOnDemandPlayer&seminarID=#local.seminarID#&enrollmentID=#local.strSeminar.qrySeminar.enrollmentID#&orgCode=#local.semWeb.orgcode#'); }
		</cfif>
		<cfloop query="local.strCredit.qryCredit">
			var #ToScript(local.JSStructCreditInfo[local.strCredit.qryCredit.authorityID],"credit_a#local.strCredit.qryCredit.authorityID#")#
		</cfloop>
		function setCreditDetails(aID) { $('##creditdetailbox ##tab'+ aID +'.tab-pane').html(eval('credit_a' + aID)); return false; }
		function showBlockedPopup(pg) { self.location.href=pg + '&nopop=1'; }

		<cfif NOT local.isBot and len(local.videoPreviewInfo.videoPreviewLink)>
			var videoPlayer;

			function initSWVideoPreviewModel() {
				videoPlayer = videojs('previewPlayer', {
					controls: true,
					autoplay: true,
					preload: 'none',
					height: 350,
				});
				$('.swProgramDetails .swvideopreview').on('click', function() {
					var videoSRC = $(this).attr('data-swvideopreviewlink');
					var videoDisplayName = $(this).attr('data-swvidepreviewdisplayname');
					videoPlayer.src(videoSRC);
					$('##swVideoPreviewLabel').html(videoDisplayName);
				});
				$('##swVideoPreviewModal').on('hidden', function () {
					videoPlayer.pause();
				});
			}
			$(function() { initSWVideoPreviewModel(); });
		</cfif>

		$(function() {
			<cfloop query="local.strCredit.qryCredit">
				setCreditDetails(#local.strCredit.qryCredit.authorityID#);
			</cfloop>
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.onDemandContentJS#">

<cfif len(local.strSeminar.qrySeminar.seminarSubTitle)>
	<cfset local.shareThisTitleAndImage = 'st_title="#encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarName)#: #encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarSubTitle)#"'>
<cfelse>
	<cfset local.shareThisTitleAndImage = 'st_title="#encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarName)#"'>
</cfif>
<cfif len(local.programFeaturedImagePath)>
	<cfset local.shareThisTitleAndImage = '#local.shareThisTitleAndImage# st_image="#local.semweb.baseurl##local.programFeaturedImagePath#"'>
</cfif>

<cfoutput>
<div class="swProgramDetailsHeader">
	<div class="row-fluid swCatalogSeparator">
		<cfsavecontent variable="local.shareBoxContent">
			<cfoutput>
			<div class="flex swCatalogShareBox <cfif NOT len(local.programFeaturedImagePath)>pull-right</cfif>">
				<p class="mb-0"><span class="muted">Share this program:</span></p>
				<ul class="flex">
					<li><span class='st_facebook_large' #local.shareThisTitleAndImage#></span></span></li>
					<li><span class='st_linkedin_large' #local.shareThisTitleAndImage#></span></li>
					<li><span class='st_email_large' #local.shareThisTitleAndImage# st_summary="Check out this program offered by #local.semWeb.qrySWP.description#"></span></li>
					<li><span class='st_twitter_large' #local.shareThisTitleAndImage#></span></li>
				</ul>
			</div>
			</cfoutput>
		</cfsavecontent>

		<cfif local.strSeminar.qryLearningObjectives.recordCount>
			<cfsavecontent variable="local.learningObjectivesContent">
				<cfoutput>
				<p><span class="lead"><strong>What You Will Learn</strong></span></p>
				<ul class="lead">
					<cfloop query="local.strSeminar.qryLearningObjectives">
						<li>#local.strSeminar.qryLearningObjectives.objective#</li>
					</cfloop>
				</ul>
				</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfif len(local.programFeaturedImagePath)>
			<cfsavecontent variable="local.imageAndShareProgramContent">
			<cfoutput>
				<div class="swCatalogImgBox">
					<img src="#local.programFeaturedImagePath#" alt="">
					<cfif NOT local.isBot and len(local.videoPreviewInfo.videoPreviewLink)>
						<div class="overlay">
							<a href="##swVideoPreviewModal" class="swvideopreview tooltip-icon" data-toggle="modal" rel="tooltip" data-placement="right" title="Preview" data-swvideopreviewlink="#jsStringFormat(local.videoPreviewInfo.videoPreviewLink)#" data-swvidepreviewdisplayname="#jsStringFormat(local.videoPreviewInfo.videoPreviewDisplayName)#">
								<i class="bi bi-play-circle" aria-hidden="true"></i>
							</a>
						</div>
					</cfif>
					<div class="swCatalogIconBox">
						<cfif NOT local.isBot>
							<a href="javascript:void(0);" class="swHeartIcon">
								<i class="sw_saveforlater bi <cfif attributes.data.isSavedProgram>bi-heart-fill swRed<cfelse>bi-heart</cfif>" data-swprogramid="#local.seminarID#" data-swprogramtype="swod" data-swsaveforlatermode="detail"></i>
							</a>
						</cfif>
					</div>
				</div>
				#local.shareBoxContent#
			</cfoutput>
			</cfsavecontent>
		</cfif>

		<cfif len(local.programFeaturedImagePath)>
			<div class="span3 swProgramDetailsHeader-img-section hidden-phone">
				#local.imageAndShareProgramContent#
			</div>
		</cfif>
		<div class="<cfif len(local.programFeaturedImagePath)>span9<cfelse>span12</cfif>">
			<div class="swProgramDetailsHeader-right">
				<div class="swOnDemand swOnDemandBorder sw-brand-label"><i class="bi bi-play-circle" aria-hidden="true"></i>#attributes.data.qrySWP.brandSWODTab#</div>
				<h1 class="swPrimary">
					#encodeForHTML(local.strSeminar.qrySeminar.seminarName)# 
					<cfif NOT len(local.programFeaturedImagePath)>
						<cfif NOT local.isBot>
							<a href="javascript:void(0);" class="muted swHeartIcon">
								<i class="sw_saveforlater bi <cfif attributes.data.isSavedProgram>bi-heart-fill swRed<cfelse>bi-heart</cfif>" data-swprogramid="#local.seminarID#" data-swprogramtype="swod" data-swsaveforlatermode="detail"></i>
							</a>
						</cfif>
						<cfif NOT local.isBot and len(local.videoPreviewInfo.videoPreviewLink)>
							<a href="##swVideoPreviewModal" class="muted swPrimaryHover tooltip-icon swvideopreview" data-toggle="modal" rel="tooltip" data-placement="right" title="Preview" data-swvideopreviewlink="#jsStringFormat(local.videoPreviewInfo.videoPreviewLink)#" data-swvidepreviewdisplayname="#jsStringFormat(local.videoPreviewInfo.videoPreviewDisplayName)#">
								<i class="bi bi-play-circle" aria-hidden="true"></i>
							</a>
						</cfif>
					</cfif>
				</h1>
				<cfif len(local.strSeminar.qrySeminar.seminarSubTitle)>
					<h1><small class="swPrimary">#encodeForHTML(local.strSeminar.qrySeminar.seminarSubTitle)#</small></h1>
				</cfif>
				<cfif local.strSeminar.qryLearningObjectives.recordCount>
					<div class="hidden-phone">
						#local.learningObjectivesContent#
					</div>
				</cfif>
				<cfif NOT len(local.programFeaturedImagePath)>
					#local.shareBoxContent#
				</cfif>
			</div>
		</div>
		<!--- featured image mobile view --->
		<cfif len(local.programFeaturedImagePath)>
			<div class="span12 swProgramDetailsHeader-img-section visible-phone">
				#local.imageAndShareProgramContent#
			</div>
		</cfif>
	</div>
	<cfif local.strSeminar.qryLearningObjectives.recordCount>
		<div class="visible-phone swProgramDetailsHeader-right">
			#local.learningObjectivesContent#
		</div>
	</cfif>
</div>

<cfsavecontent variable="local.jumpToOptionsContent">
	<cfoutput>
	<cfif local.strSeminar.qrySponsors.recordCount>
		<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="sponsorsSection" data-label="Sponsors">Sponsors</a></li>
	</cfif>
	<cfif len(local.speakerBio)>
		<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="presentersSection" data-label="Presenters">Presenters</a></li>
	</cfif>
	<cfif local.qryOutline.recordcount>
		<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="componentsSection" data-label="Components">Components</a></li>
	</cfif>
	<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="creditSection" data-label="Credit">Credit</a></li>
	<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="howToAttendSection" data-label="How to Attend">How to Attend</a></li>
	<cfif local.qrySWP.handlesOwnPayment is 0>
		<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="moreSection" data-label="More">More</a></li>
	</cfif>
	</cfoutput>
</cfsavecontent>

<cfsavecontent variable="local.jumpToOptionsContentMobile">
	<cfoutput>
	<li><a href="javascript:void(0);" class="swPrimary boldHoverText" data-targetid="summarySection" data-label="Summary">Summary</a></li>
	#local.jumpToOptionsContent#
	</cfoutput>
</cfsavecontent>

<div class="content">
	<div class="row-fluid">
		<div class="span4 pull-right swCatalogContLightSection">
			<cfif attributes.data.hasPendingRegistrations and NOT local.isBot>
				<div class="alert alert-block text-center">
					You have <a href="#attributes.event.getValue('mainurl')#&panel=showCart" class="swPrimary">pending registrations</a>.
				</div>
			</cfif>

			<cfset local.showPriceAndDuration = false>
			<cfset local.canRegisterNow = false>

			<cfsavecontent variable="local.registerSectionContent">
				<cfoutput>
				<cfif local.strSeminar.qrySeminar.isRegistered and local.preReqFulfilled>
					<h4 class="swPrimary text-center">You're Registered</h4>
				<cfelseif local.strSeminar.qrySeminar.isRegistered>
					<h4 class="swPrimary text-center">Awaiting Prerequisties</h4>
				<cfelseif NOT local.isRegOpen>
					<h4 class="swPrimary text-center">Registration</h4>
				</cfif>

				<cfif application.objPlatform.hasMinimumOSVersion("iOS",10) and NOT local.isBot>
					<cfif local.strSeminar.qrySeminar.isRegistered and local.preReqFulfilled>
						<a name="btnEnterProgram" href="javascript:enterSWODProgram();" class="swPrimaryBkgd swWhite swPrimaryBorder cart-btn btn btn-primary">Enter Program <i class="bi bi-chevron-right"></i></a>
						<cfif local.isRegOpen>
							<cfset local.showPriceAndDuration = true>
							<p class="text-center"><a href="javascript:enrollNow();" class="swPrimary">Register a colleague</a> for this program.</p>
						</cfif>
					<cfelseif local.strSeminar.qrySeminar.isRegistered>
						<p class="text-center">Our records show you have not yet completed the listed prerequisites for this program.<br/><br/>Once the prerequisites are completed, refresh this page to begin this program.</p>
						<cfif local.isRegOpen>
							<cfset local.showPriceAndDuration = true>
							<p class="text-center"><a href="javascript:enrollNow();" class="swPrimary">Register a colleague</a> for this program.</p>
						</cfif>
					<cfelseif local.strSeminar.qrySeminarPrices.recordcount is 0 or (local.strSeminar.qrySeminarPrices.recordcount is 1 and local.strSeminar.qrySeminarPrices.price lt 0)>
						<p class="text-center">This program is not available for purchase at this time. If you feel this is an error, contact customer service at <nobr>#local.qrySWP.supportPhone#</nobr>.</p>
					<cfelse>
						<cfif local.isRegOpen>
							<cfset local.showPriceAndDuration = true>
							<cfset local.canRegisterNow = true>
							<p class="text-center">Register now for immediate access to this program.</p>
							<a name="btnRegNow" href="javascript:enrollNow();" class="swPrimaryBkgd swWhite swPrimaryBorder cart-btn btn btn-primary">Register Now <i class="bi bi-chevron-right"></i></a>
						<cfelse>
							<p class="text-center">This program is not accepting registrations at this time.</p>
						</cfif>
						<cfif attributes.data.memberID is 0>
							<p class="text-center">Already Registered?</p>
							<a name="btnLogin" href="javascript:gotoLogin();" class="swPrimaryBkgd swWhite swPrimaryBorder cart-btn btn btn-primary">Login <i class="bi bi-chevron-right"></i></a>
						</cfif>
					</cfif>
				<cfelseif NOT local.isBot>
					<p class="text-center">Please note that in order to access this course on this mobile device, you must have downloaded iOS 10 or higher. Once you perform the software update, you may return here to purchase and enter the program.</p>
				</cfif>
				</cfoutput>
			</cfsavecontent>

			<cfif local.canRegisterNow>
				#local.registerSectionContent#
			<cfelse>
				<div class="well">#local.registerSectionContent#</div>
			</cfif>

			<div class="visible-phone">
				<p><small><strong>JUMP TO:</strong></small></p>
				<ul class="swCatalogTabList jumpToList">
					#local.jumpToOptionsContentMobile#
				</ul>
			</div>
			<cfif local.showPriceAndDuration>
				<cfif local.strSeminar.qrySeminarPrices.recordcount>
					<div class="swCatalogRightBlocks">
						<h5 class="swPrimary"><i class="bi bi-tag-fill" aria-hidden="true"></i>Price</h5>
						<p>
							<cfloop query="local.strSeminar.qrySeminarPrices">
								<cfif local.strSeminar.qrySeminarPrices.price gte 0>
									<cfif local.strSeminar.qrySeminarPrices.price is 0>
										#replace(local.strSeminar.qrySeminar.freeRateDisplay,".00","")#
									<cfelseif local.strSeminar.qrySeminarPrices.price gt 0>
										#replace(dollarformat(local.strSeminar.qrySeminarPrices.price),".00","")#<cfif local.strSeminar.qrySeminar.showUSD> USD</cfif>
									</cfif>
									<cfif len(local.strSeminar.qrySeminarPrices.description)> <cfif local.strSeminar.qrySeminarPrices.price gt 0 or len(local.strSeminar.qrySeminar.freeRateDisplay)>for </cfif>#local.strSeminar.qrySeminarPrices.description#</cfif>
									<br/>
								</cfif>
							</cfloop>
						</p>
					</div>
				</cfif>
				<cfif len(local.strSeminar.qrySeminar.seminarLength) AND local.strSeminar.qrySeminar.seminarLength GT 0>
					<div class="swCatalogRightBlocks">
						<h5 class="swPrimary"><i class="bi bi-hourglass-split" aria-hidden="true"></i>#local.strSeminar.qrySeminar.seminarLength# minutes</h5>
					</div>
				</cfif>
			</cfif>
			<cfif local.qryPreReqs.recordcount>
				<div class="swCatalogRightBlocks programPrerequisites">
					<h5 class="swPrimary"><i class="bi bi-check-all" aria-hidden="true"></i>Prerequisites</h5>
					<div class="muted line-height-sm"><small>You must complete the following prior to beginning this program:</small></div>
					<p>
						<cfloop query="local.qryPreReqs">
							<a href="#attributes.event.getValue('mainurl')#&panel=showSWOD&seminarID=#local.qryPreReqs.seminarID#" class="swPrimary">#encodeForHTML(local.qryPreReqs.seminarName)#</a><br/>
						</cfloop>
					</p>
				</div>
			</cfif>
			<cfif local.qryBundles.recordcount>
				<div class="swCatalogRightBlocks relatedBundles">
					<h5 class="swPrimary"><i class="bi bi-basket-fill" aria-hidden="true"></i>Bundles</h5>
					<div class="muted line-height-sm"><small>Program is also part of <cfif local.qryBundles.recordcount is 1>this bundle<cfelse>these bundles</cfif>:</small></div>
					<p>
						<cfloop query="local.qryBundles">
							<a href="/?pg=semwebCatalog&panel=showBundle&bundleID=#local.qryBundles.bundleID#" class="swPrimary">#local.qryBundles.bundleName#</a><br/>
						</cfloop>
					</p>
				</div>
			</cfif>
			<div class="swCatalogRightBlocks datePublished">
				<h5 class="swPrimary"><i class="bi bi-clock-fill" aria-hidden="true"></i>Date Published</h5>
				<p>#DateFormat(local.strSeminar.qrySeminar.dateOrigPublished,"mmmm d, yyyy")#</p>
			</div>
			<div class="swCatalogRightBlocks">
				<h5 class="swPrimary"><i class="bi bi-briefcase-fill" aria-hidden="true"></i>Publisher</h5>
				<p>#local.strSeminar.qrySeminar.description#</p>
			</div>
			<cfif local.strSeminar.qryLinkedCategories.recordCount>
				<div class="swCatalogRightBlocks">
					<h5 class="swPrimary"><i class="bi bi-pencil-fill" aria-hidden="true"></i>Subjects</h5>
					<p>
						<cfloop query="local.strSeminar.qryLinkedCategories">
							#local.strSeminar.qryLinkedCategories.categoryName#<cfif local.strSeminar.qryLinkedCategories.currentRow neq local.strSeminar.qryLinkedCategories.recordCount>, </cfif>
						</cfloop>
					</p>
				</div>
			</cfif>
			<div class="swCatalogRightBlocks">
				<h5 class="swPrimary"><i class="bi bi-question-circle-fill" aria-hidden="true"></i>Questions</h5>
				<p>
					For immediate assistance please consult our <a href="/?pg=semwebCatalog&panel=showFAQ" class="swPrimary">FAQ page</a>. 
					If you're unable to find the answer you need, please call #local.qrySWP.supportPhone# (#local.qrySWP.supportHours#) or <a href="mailto:#local.qrySWP.supportEmail#" class="swPrimary">e-mail customer service</a>.
				</p>
			</div>
		</div>
		<div class="span8 content-left-section">
			<div class="hidden-phone">
				<p><small><strong>JUMP TO:</strong></small></p>
				<ul class="swCatalogTabList jumpToList">
					#local.jumpToOptionsContent#
				</ul>
			</div>
			<div class="swCatalogSubBlocks swDetailSummary">
				<h3 class="swPrimary">Summary</h3>
				<div class="expand expandSection" id="summarySection">
					<p>
						#local.strSeminar.qrySeminar.SeminarDesc#
						<div class="swProgramCode muted"><strong>#local.strSeminar.qrySeminar.programCode#</strong></div>
					</p>
					<a href="javascript:void(0);" class="swPrimary swAExpandSummary">Expand summary</a>
				</div>
			</div>
			<cfif local.strSeminar.qrySponsors.recordCount>
				<cfset local.qrySponsors = local.strSeminar.qrySponsors>
				<div class="swCatalogSubBlocks swDetailSponsors" id="sponsorsSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
					<h3 class="swPrimary">Sponsors</h3>
					<cfinclude template="/views/semwebCatalog/responsive/swProgramDetailsSpeakersCommon.cfm">
				</div>
			</cfif>
			<cfif len(local.speakerBio)>
				<div class="swCatalogSubBlocks swDetailSpeakers" id="presentersSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
					<h3 class="swPrimary">Presenters</h3>
					#local.speakerBio#
				</div>
			</cfif>
			<cfif local.qryOutline.recordcount>
				<cfquery name="local.qryTitlesInSem" dbtype="query">
					select distinct titleID from [local].qryOutline
				</cfquery>
				<div class="swCatalogSubBlocks swDetailComponents" id="componentsSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
					<h3 class="swPrimary">Program Titles and Supporting Materials</h3>
					<p>This program contains the following <cfif local.qryTitlesInSem.recordcount gt 1>titles and </cfif>components:</p>
					<div class="accordion componentsAccordion">
						<cfoutput query="local.qryOutline" group="titleID">
							<cfset local.currentFileGroup = "">
							<cfset local.mediaFilesShown = false>
							<cfset local.downloadFilesShown = false>
							<cfset local.pvrFilesShown = false>
							<cfif local.qryTitlesInSem.recordcount gt 1>
								<div class="accordion-group" id="accordion#local.qryOutline.titleID#">
									<div class="accordion-heading">
										<a class="accordion-toggle collapsed sw-d-flex swPrimary" data-toggle="collapse" href="##collapse#local.qryOutline.titleID#" aria-expanded="true">
											<b class="mr-auto">#local.qryOutline.titleName#</b> <i class="bi bi-chevron-compact-down accordion-toggle-icon sw-ml-2"></i>
										</a>
									</div>
									<div id="collapse#local.qryOutline.titleID#" class="accordion-body collapse">
										<div class="accordion-inner">
							</cfif>

							<cfoutput>
								<!--- Determine file group for single title programs --->
								<cfset local.fileGroup = "">
								<cfif local.qryOutline.fileType eq "video" OR local.qryOutline.fileType eq "audio">
									<cfset local.fileGroup = "media">
								<cfelseif local.qryOutline.fileType eq "paper" AND local.qryOutline.fileTitle eq "Slides">
									<cfset local.fileGroup = "pvr">
								<cfelse>
									<cfset local.fileGroup = "download">
								</cfif>

								<!--- Show section header when file group changes --->
								<cfif local.currentFileGroup neq local.fileGroup>
									<cfset local.currentFileGroup = local.fileGroup>
									<cfif local.fileGroup eq "media" AND NOT local.mediaFilesShown>
										<h5 class="swPrimary mt-3 mb-2">Media Files</h5>
										<cfset local.mediaFilesShown = true>
									<cfelseif local.fileGroup eq "download" AND NOT local.downloadFilesShown>
										<h5 class="swPrimary mt-3 mb-2">Downloadable Files</h5>
										<cfset local.downloadFilesShown = true>
									<cfelseif local.fileGroup eq "pvr" AND NOT local.pvrFilesShown>
										<h5 class="swPrimary mt-3 mb-2">PVR Images</h5>
										<cfset local.pvrFilesShown = true>
									</cfif>
								</cfif>

								<div class="titleFiles <cfif local.qryTitlesInSem.recordcount eq 1>singleEntry</cfif>">
									<cfif local.qryOutline.fileType eq "video">
										<div class="icon"><i class="bi bi-camera-reels muted"></i> #local.qryOutline.fileTitle#</div>
									<cfelseif local.qryOutline.fileType eq "audio">
										<div class="icon"><i class="bi bi-volume-down muted"></i> #local.qryOutline.fileTitle#</div>
									<cfelse>
										<cfset local.xmlFormats = XMLSearch(local.qryOutline.formatsAvailable,"/formats/format[not (@ext='flv' or @ext='swf' or @ext='pvr')]")>
										<cfif arraylen(local.xmlFormats)>
											<cfloop from="1" to="#arraylen(local.xmlFormats)#" index="local.typeNum">
												<div class="icon">
													<cfif StructKeyExists(local.faClassesFiles,local.xmlFormats[local.typeNum].xmlAttributes.ext)>
														<i class="#local.faClassesFiles[local.xmlFormats[local.typeNum].xmlAttributes.ext]# muted"></i>
													<cfelse>
														<i class="bi bi-file-earmark muted"></i>
													</cfif>
													#local.qryOutline.fileTitle#
												</div>
											</cfloop>
										<cfelse>
											<div class="icon"><i class="bi bi-file-earmark-text muted"></i> #local.qryOutline.fileTitle#</div>
										</cfif>
									</cfif>
								</div>
							</cfoutput>

							<cfif local.qryTitlesInSem.recordcount gt 1>
										</div>
									</div>
								</div>
							</cfif>
						</cfoutput>
					</div>
				</div>
			</cfif>
			<div class="swCatalogSubBlocks swDetailCredit" id="creditSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
				<h3 class="swPrimary">Credit</h3>
				<cfif local.strCredit.qryCreditDistinct.recordcount>
					<p>
						If applicable, you may obtain credit in multiple jurisdictions simultaneously for this program (see pending/approved list below).
						<cfif local.strSeminar.qrySeminar.offerCertificate>
							If electing credit for this program, registrants in jurisdictions not listed below will receive a 
							Certificate of Completion that may or may not meet credit requirements in other jurisdictions.
						</cfif>
						Where applicable, credit will be only awarded to a paid registrant completing all the requirements of the program as determined by the selected accreditation authority.
					</p>
					<cfif local.strCredit.qryCreditDistinct.recordCount gt 1>
						<p>Click on jurisdiction for specific details:</p>
					</cfif>
					<ul id="tab" data-toggle="buttons-radio" class="swCatalogTabList">
						<cfloop query="local.strCredit.qryCreditDistinct">
							<li <cfif local.strCredit.qryCreditDistinct.currentRow eq 1>class="active"</cfif>><a href="##tab#local.strCredit.qryCreditDistinct.authorityID#" class="swPrimary" data-toggle="tab">#local.strCredit.qryCreditDistinct.authorityCode#</a></li>
						</cfloop>
					</ul>
					<div class="tab-content" id="creditdetailbox">
						<cfloop query="local.strCredit.qryCreditDistinct">
							<div class="tab-pane <cfif local.strCredit.qryCreditDistinct.currentRow eq 1>active</cfif>" id="tab#local.strCredit.qryCreditDistinct.authorityID#"></div>
						</cfloop>
					</div>
				<cfelse>
					<p>
						This program <i>has not been submitted</i> for credit in any jurisdiction. 
						<cfif local.strSeminar.qrySeminar.offerCertificate>
							Registrants will receive a Certificate of Attendance/Completion that may or may not meet credit requirements in various jurisdictions.
						</cfif>
					</p>
				</cfif>
			</div>
			<div class="swCatalogSubBlocks swDetailAttend" id="howToAttendSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
				<h3 class="swPrimary">How to Attend</h3>
				<p>
					Join the self-paced program from your office, home, or hotel room using a computer and high speed 
					internet connection. You may start and stop the program at your convenience, continue where you left off, and 
					review supporting materials as often as you like. 
					<b>Please note: Internet Explorer is no longer a supported browser.</b> We recommend using Google Chrome, Mozilla Firefox or Safari for best results.
				</p>
				<p>
					<strong><span class="muted">Technical Requirements</span></strong><br>
					You may access this course on a computer or mobile device with high speed internet (iPhones require iOS 10 or higher). Recommended browsers are Google Chrome or Mozilla Firefox.
				</p>
			</div>
			<cfif local.qrySWP.handlesOwnPayment is 0>
				<div class="swCatalogSubBlocks swDetailMore" id="moreSection"> <a href="javascript:backToTop();" class="muted visible-phone pull-right"><i class="bi bi-chevron-compact-up"></i> back to top</a>
					<h3 class="swPrimary">More</h3>
					<p>
						<strong><span class="muted">Refund Policy</span></strong><br>
						SeminarWeb and #local.semWeb.orgname# programs are non-refundable.
					</p>
					<p>
						<strong><span class="muted">Privacy Statement</span></strong><br>
						We respect and are committed to protecting your privacy. <a href="javascript:sw_showPrivacyStatement();" class="swPrimary">Read Statement</a>.
					</p>
				</div>
			</cfif>
		</div>
	</div>
	<cfif local.seminarSuggestions.recordcount>
		<div class="swOtherPrgms">
			<h4 class="muted">YOU MAY ALSO BE INTERESTED IN...</h4>
			<div class="row-fluid">
				<cfloop query="local.seminarSuggestions">
					<cfset local.otherPgmsFeaturedThumbImageFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.seminarSuggestions.featureImageOrgCode)#/#LCASE(local.seminarSuggestions.featureImageSiteCode)#/featuredimages/thumbnails/">
					<cfset local.otherPgmsFeaturedThumbImageRootPath = "/userassets/#LCASE(local.seminarSuggestions.featureImageOrgCode)#/#LCASE(local.seminarSuggestions.featureImageSiteCode)#/featuredimages/thumbnails/">
					<cfset local.thisProgramFeaturedImagePath = "">
					<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.seminarSuggestions.featureImageID) AND fileExists("#local.otherPgmsFeaturedThumbImageFullRootPath##local.seminarSuggestions.featureImageID#-#local.seminarSuggestions.featureImageSizeID#.#local.seminarSuggestions.fileExtension#")>
						<cfset local.thisProgramFeaturedImagePath = "#local.otherPgmsFeaturedThumbImageRootPath##local.seminarSuggestions.featureImageID#-#local.seminarSuggestions.featureImageSizeID#.#local.seminarSuggestions.fileExtension#">
					<cfelseif local.seminarSuggestions.seminarType eq "On-Demand" and len(attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWODFeaturedImagePath)>
						<cfset local.thisProgramFeaturedImagePath = attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWODFeaturedImagePath>
					<cfelseif local.seminarSuggestions.seminarType eq "Webinar" and len(attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWLFeaturedImagePath)>
						<cfset local.thisProgramFeaturedImagePath = attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWLFeaturedImagePath>
					</cfif>

					<div class="span4">
						<div class="swCatalogInnerPrgms <cfif NOT len(local.thisProgramFeaturedImagePath)>no-image</cfif>">
							<div class="row-fluid">
								<cfif len(local.thisProgramFeaturedImagePath)>
									<div class="span3">
										<img src="#local.thisProgramFeaturedImagePath#" alt="">
									</div>
								</cfif>
								<div class="<cfif len(local.thisProgramFeaturedImagePath)>span9<cfelse>span12</cfif>">
									<h6>
										<cfif local.seminarSuggestions.seminarType eq "On-Demand">
											<cfset local.programLink = "?pg=semwebCatalog&panel=showSWOD&seminarid=#local.seminarSuggestions.seminarID#">
										<cfelse>
											<cfset local.programLink = "?pg=semwebCatalog&panel=showLive&seminarid=#local.seminarSuggestions.seminarID#">
										</cfif>
										<a href="#local.programLink#" class="swPrimary" target="_blank">#encodeForHTML(local.seminarSuggestions.seminarName)#</a>
									</h6>
									<div class="flex">
										<cfif local.seminarSuggestions.seminarType eq "On-Demand">
											<p class="mb-0 swOnDemand small"><i class="bi bi-play-circle" aria-hidden="true"></i>#attributes.data.qrySWP.brandSWODTab#</p>
										<cfelse>
											<p class="mb-0 swWebinar small"><i class="bi bi-laptop" aria-hidden="true"></i>#attributes.data.qrySWP.brandSWLTab#</p>
										</cfif>
										<p class="mb-0 muted small tooltip-icon" title="<cfif local.seminarSuggestions.seminarType eq "On-Demand">Published on<cfelse>Being Held On </cfif> #dateformat(local.seminarSuggestions.DatePublished, "mmm dd")#">
											<i class="bi bi-calendar-fill" aria-hidden="true"></i><span class="text-nowrap">#dateformat(local.seminarSuggestions.DatePublished, "mmm dd")#</span>
										</p>
									</div>
								</div>
							</div>
						</div>
					</div>
					<cfif local.seminarSuggestions.currentRow mod 3 is 0 and local.seminarSuggestions.currentRow neq local.seminarSuggestions.recordCount>
						</div><div class="row-fluid">
					</cfif>
				</cfloop>
			</div>
		</div>
	</cfif>

	<cfif NOT local.isBot and len(local.videoPreviewInfo.videoPreviewLink)>
		<!--- Video Preview Modal --->
		<div id="swVideoPreviewModal" class="modal hide fade" tabindex="-1" role="dialog" aria-labelledby="swVideoPreviewLabel" aria-hidden="true">
			<div class="modal-header">
				<button type="button" class="close" data-dismiss="modal" aria-hidden="true">&times;</button>
				<div class="badge badge-info">Preview</div>
				<h4 id="swVideoPreviewLabel"></h4>
			</div>
			<div class="modal-body sw-pt-0">
				<video id="previewPlayer" class="video-js sw-w-100">
					<p class="vjs-no-js">To view this video please enable JavaScript, and consider upgrading to a web browser that <a href="https://videojs.com/html5-video-support/" target="_blank">supports HTML5 video</a></p>
				</video>
			</div>
		</div>
	</cfif>
</div>
</cfoutput>