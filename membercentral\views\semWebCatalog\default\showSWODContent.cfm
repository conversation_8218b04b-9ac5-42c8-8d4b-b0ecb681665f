<cfset local.strSeminar = attributes.data.strSeminar>
<cfset local.qryPreReqs = attributes.data.qryPreReqs>
<cfset local.strCredit = attributes.data.strCredit>
<cfset local.JSStructCreditInfo = attributes.data.JSStructCreditInfo>
<cfset local.qryOutline = attributes.data.qryOutline>
<cfset local.qryBundles = attributes.data.qryBundles>
<cfset local.seminarSuggestions = attributes.data.seminarSuggestions>
<cfset local.speakerBio = attributes.data.speakerBio>
<cfset local.qrySWP = attributes.data.qrySWP>
<cfset local.semWeb = attributes.data.semWeb>
<cfset local.isRegOpen = attributes.data.isRegOpen>
<cfset local.preReqFulfilled = attributes.data.preReqFulfilled>
<cfset local.videoPreviewInfo = attributes.data.videoPreviewInfo>
<cfset local.faClassesFiles = attributes.data.faClassesFiles>
<cfset local.seminarID = attributes.data.seminarID>
<cfif isDefined("session.mcstruct.deviceProfile.is_bot") AND session.mcstruct.deviceProfile.is_bot is 1>
	<cfset local.isBot = 1>
<cfelse>
	<cfset local.isBot = 0>
</cfif>
<cfset local.featuredThumbImageFullRootPath = attributes.data.featuredThumbImageFullRootPath>
<cfset local.featuredThumbImageRootPath = attributes.data.featuredThumbImageRootPath>
<cfset local.programFeaturedImagePath = "">
<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.strSeminar.qrySeminar.featureImageID) AND fileExists("#local.featuredThumbImageFullRootPath##local.strSeminar.qrySeminar.featureImageID#-#local.strSeminar.qrySeminar.featureImageSizeID#.#local.strSeminar.qrySeminar.featureImageFileExtension#")>
	<cfset local.programFeaturedImagePath = "#local.featuredThumbImageRootPath##local.strSeminar.qrySeminar.featureImageID#-#local.strSeminar.qrySeminar.featureImageSizeID#.#local.strSeminar.qrySeminar.featureImageFileExtension#">
<cfelseif len(attributes.data.defaultFeaturedImagePathsStr.defaultSWODFeaturedImagePath)>
	<cfset local.programFeaturedImagePath = attributes.data.defaultFeaturedImagePathsStr.defaultSWODFeaturedImagePath>
</cfif>

<cfsavecontent variable="local.onDemandContentJS">
	<cfoutput>
	<script type="text/javascript" src="/assets/common/javascript/swfobject.js"></script>
	<script language="javascript">
		<cfif attributes.data.isRegOpen>
			function enrollNow() { self.location.href='#attributes.event.getValue('mainurl')#&panel=reg&item=SWOD-#local.strSeminar.qrySeminar.seminarID#'; }
		</cfif>
		<cfif local.strSeminar.qrySeminar.isRegistered and local.preReqFulfilled and NOT local.isBot>
			function enterSWODProgram() { window.open('/?pg=swOnDemandPlayer&seminarID=#local.seminarID#&enrollmentID=#local.strSeminar.qrySeminar.enrollmentID#&orgCode=#local.semWeb.orgcode#'); }
		</cfif>
		<cfif len(local.videoPreviewInfo.videoPreviewLink) and NOT local.isBot>
			function openSWVideoPreview(previewVideoURL,title) { window.open(previewVideoURL, title, "width=480,height=300,scrollbars=no,titlebar=no,menubar=no,toolbar=no,location=no,status=no,resizable=yes"); }
		</cfif>
		function showCreditDetail(aID) { document.getElementById('creditdetailbox').innerHTML = eval('credit_a' + aID); return false; }
		function showBlockedPopup(pg) { self.location.href=pg + '&nopop=1'; }	
		<cfloop query="local.strCredit.qryCredit">
			var #ToScript(local.JSStructCreditInfo[local.strCredit.qryCredit.authorityID],"credit_a#local.strCredit.qryCredit.authorityID#")#
		</cfloop>
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#local.onDemandContentJS#">

<cfif len(local.strSeminar.qrySeminar.seminarSubTitle)>
	<cfset local.shareThisTitleAndImage = 'st_title="#encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarName)#: #encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarSubTitle)#"'>
<cfelse>
	<cfset local.shareThisTitleAndImage = 'st_title="#encodeForHTMLAttribute(local.strSeminar.qrySeminar.seminarName)#"'>
</cfif>
<cfif len(local.programFeaturedImagePath)>
	<cfset local.shareThisTitleAndImage = '#local.shareThisTitleAndImage# st_image="#local.semweb.baseurl##local.programFeaturedImagePath#"'>
</cfif>

<!--- price info --->
<cfsavecontent variable="local.priceinfo">
	<cfoutput query="local.strSeminar.qrySeminarPrices">
		<cfif local.strSeminar.qrySeminarPrices.price gte 0>
			<cfif local.strSeminar.qrySeminarPrices.price is 0>
				#replace(local.strSeminar.qrySeminar.freeRateDisplay,".00","")#
			<cfelseif local.strSeminar.qrySeminarPrices.price gt 0>
				#replace(dollarformat(local.strSeminar.qrySeminarPrices.price),".00","")#<cfif local.strSeminar.qrySeminar.showUSD> USD</cfif>
			</cfif>
			<cfif len(local.strSeminar.qrySeminarPrices.description)> <cfif local.strSeminar.qrySeminarPrices.price gt 0 or len(local.strSeminar.qrySeminar.freeRateDisplay)>for </cfif>#local.strSeminar.qrySeminarPrices.description#</cfif><br/>
		</cfif>
	</cfoutput>
	<br/>
</cfsavecontent>

<cfoutput>
<!--- content --->
<div id="sw_listing" class="tsAppBodyText" align="center">
	<div style="width:95%;" align="left">
		<div class="headersection sw-d-flex">
			<cfif len(local.programFeaturedImagePath)>
				<div id="headersectionimage"><img src="#local.programFeaturedImagePath#" style="width:200px;height:200px;" alt=""></div>
			</cfif>
			<div id="headersectiontitle">
				<div class="swOnDemand sw-text-uppercase"><i class="bi bi-play-circle" aria-hidden="true"></i> #attributes.data.qrySWP.brandSWODTab#</div>
				<!--- title --->
				<h2 id="programtitle" class="swPrimary sw-mt-1">
					#encodeForHTML(local.strSeminar.qrySeminar.seminarName)#
					<cfif NOT local.isBot>
						<a href="javascript:void(0);" class="swHeartIcon swMuted">
							<i class="sw_saveforlater bi <cfif attributes.data.isSavedProgram>bi-heart-fill swRed<cfelse>bi-heart</cfif>" data-swprogramid="#local.strSeminar.qrySeminar.seminarID#" data-swprogramtype="swod" data-swsaveforlatermode="detail"></i>
						</a>
					</cfif>
				</h2>
				<cfif len(local.strSeminar.qrySeminar.seminarSubTitle) OR local.semWeb.sitecode neq "TS">
					<cfif len(local.strSeminar.qrySeminar.seminarSubTitle)>
						<h4 id="programsubtitle" class="swPrimary">#encodeForHTML(local.strSeminar.qrySeminar.seminarSubTitle)#</h4>
					</cfif>
					<cfif local.semWeb.sitecode neq "TS">
						<div>Published by #local.strSeminar.qrySeminar.description#<cfif local.semWeb.orgname neq local.strSeminar.qrySeminar.description><br/>and #local.semWeb.orgname#</cfif></div>
					</cfif>	
				</cfif>
				<cfif len(local.videoPreviewInfo.videoPreviewLink) and NOT local.isBot>
					<div class="sw-mt-2">
						<a href="##" class="swPrimary" title="Preview" onclick="openSWVideoPreview('#jsStringFormat(local.videoPreviewInfo.videoPreviewLink)#','#jsStringFormat(local.videoPreviewInfo.videoPreviewDisplayName)#')">
							<i class="bi bi-play-circle" aria-hidden="true"></i> Video Preview
						</a>
					</div>
				</cfif>
				<cfif local.strSeminar.qryLearningObjectives.recordCount>
					<div class="tsAppBodyText sw-mt-3">
						<b>#attributes.data.semWeb.qrySWP.brandLearnObjectives#</b><br/>
						<div style="margin:2px 20px 2px 0;">
							<ul class="learningobjectives sw-mt-2">
								<cfloop query="local.strSeminar.qryLearningObjectives">
									<li>#local.strSeminar.qryLearningObjectives.objective#</li>
								</cfloop>
							</ul>
						</div>
						<br/>
					</div>
				</cfif>
			</div>
		</div>
		<br clear="all">
		<br/>

		<div id="actionswrapper" class="tsAppBodyText">
			<cfif attributes.data.hasPendingRegistrations and NOT local.isBot>
				<div style="padding:3px;">
					<div class="tsAppBodyTextImportant">Reminder:</div>
					You have <a href="#attributes.event.getValue('mainurl')#&panel=showCart">pending registrations</a>.
				</div>
			</cfif>

			<div class="sidebox <cfif local.strSeminar.qrySeminar.isRegistered and local.preReqFulfilled>sideboxreg</cfif>">
			<cfif local.strSeminar.qrySeminar.isRegistered and local.preReqFulfilled>
				<div class="sideboxtitle sideboxtitlereg">You're Registered</div>
			<cfelseif local.strSeminar.qrySeminar.isRegistered>
				<div class="sideboxtitle">Awaiting Prerequisties</div>
			<cfelseif local.isRegOpen>
				<div class="sideboxtitle">Register Now</div>
			<cfelse>
				<div class="sideboxtitle">Registration</div>
			</cfif>
				<div class="sideboxbody">
					<cfif application.objPlatform.hasMinimumOSVersion("iOS",10) and NOT local.isBot>
						<cfif local.strSeminar.qrySeminar.isRegistered and local.preReqFulfilled>
							<div align="center" style="margin-top:6px;"><button type="button" name="btnEnterProgram" id="btnEnterProgram" class="tsAppBodyButton buttonreg" onclick="enterSWODProgram();">Enter Program</button></div>
							<cfif local.isRegOpen>
								<br/>
								#local.priceinfo#
								<cfif len(local.strSeminar.qrySeminar.seminarLength) AND local.strSeminar.qrySeminar.seminarLength GT 0>Duration: #local.strSeminar.qrySeminar.seminarLength# minutes<br/><br/>
								</cfif>
								<a href="javascript:enrollNow();" class="swPrimary tsAppBodyText">Register a colleague</a> for this program.
							</cfif>
						<cfelseif local.strSeminar.qrySeminar.isRegistered>
							Our records show you have not yet completed the listed prerequisites for this program.<br/><br/>
							Once the prerequisites are completed, refresh this page to begin this program.
							<cfif local.isRegOpen>
								<br/>
								#local.priceinfo#
								<cfif len(local.strSeminar.qrySeminar.seminarLength) AND local.strSeminar.qrySeminar.seminarLength GT 0>Duration: #local.strSeminar.qrySeminar.seminarLength# minutes<br/><br/>
								</cfif>
								<a href="javascript:enrollNow();" class="swPrimary tsAppBodyText">Register a colleague</a> for this program.
							</cfif>
						<cfelseif local.strSeminar.qrySeminarPrices.recordcount is 0 or (local.strSeminar.qrySeminarPrices.recordcount is 1 and local.strSeminar.qrySeminarPrices.price lt 0)>
							This program is not available for purchase at this time. 
							If you feel this is an error, contact customer service at <nobr>#local.qrySWP.supportPhone#</nobr>.
						<cfelse>
							<cfif local.isRegOpen>
								#local.priceinfo#
								<cfif len(local.strSeminar.qrySeminar.seminarLength) AND local.strSeminar.qrySeminar.seminarLength GT 0>Duration: #local.strSeminar.qrySeminar.seminarLength# minutes<br/><br/>
								</cfif>
								Register now for immediate access to this program.
								<div align="center" style="margin-top:6px;"><button type="button" name="btnRegNow" class="tsAppBodyButton" onClick="enrollNow();">Register Now</button></div>
							<cfelse>		
								<div class="tsAppBodyTextImportant">This program is not accepting registrations at this time.</div>
							</cfif>
							<cfif attributes.data.memberID is 0>
								<br/>
								<b>Already Registered?</b><br/>
								<div align="center" style="margin-top:6px;"><button type="button" name="btnRegNow" class="tsAppBodyButton" onClick="return gotoLogin();">Login</button></div>
							</cfif>
						</cfif>
					<cfelseif NOT local.isBot>
						Please note that in order to access this course on this mobile device, you must have downloaded iOS 10 or higher. Once you perform the software update, you may return here to purchase and enter the program.
					</cfif>
				</div>
			</div>
	
			<cfif local.qryBundles.recordcount>
				<br/>
				<div class="sidebox">
					<div class="sideboxtitle">Bundles Available</div>
					<div class="sideboxbody">
						This program is also offered in the following bundle<cfif local.qryBundles.recordcount gt 1>s</cfif>:<br/>
						<table>
						<cfloop query="local.qryBundles">
							<tr><td valign="top"><li></td><td><a href="#attributes.event.getValue('mainurl')#&panel=showBundle&bundleID=#local.qryBundles.bundleID#" class="swPrimary tsAppBodyText">#local.qryBundles.bundleName#</a></td></tr>
						</cfloop>
						</table>
					</div>
				</div>				
			</cfif>
	
			<br/>
			<div class="sidebox">
				<div class="sideboxtitle">Tell a Colleague!</div>
				<div class="sideboxbody">
				 	<span class='st_facebook_large' #local.shareThisTitleAndImage#></span>
					<span class='st_linkedin_large' #local.shareThisTitleAndImage#></span>
					<span class='st_email_large' #local.shareThisTitleAndImage# st_summary="Check out this program offered by #local.semWeb.qrySWP.description#"></span>
					<span class='st_twitter_large' #local.shareThisTitleAndImage#></span>
				</div>
			</div>
			<br/>
			<div class="sidebox">
				<div class="sideboxtitle">Questions?</div>
				<div class="sideboxbody">
					For immediate assistance please consult our <a href="/?pg=semwebCatalog&panel=showFAQ" class="swPrimary tsAppBodyText">FAQ page</a>. 
					<br/><br/>
					If you're unable to find the answer you need, please call #local.qrySWP.supportPhone# (#local.qrySWP.supportHours#) or 
					<a href="mailto:#local.qrySWP.supportEmail#" class="swPrimary tsAppBodyText">e-mail customer service</a>.
				</div>
			</div>
		</div>
	
		<div class="tsAppBodyText">
			<h5 class="swPrimary">Summary</h5>
			<div style="margin:2px 20px 2px 0;">#local.strSeminar.qrySeminar.SeminarDesc#</div>
			<div class="swMuted" style="margin-bottom:2px"><strong>#local.strSeminar.qrySeminar.programCode#</strong></div>
			<br/>
		</div>

		<cfif local.strSeminar.qrySponsors.recordCount>
			<cfset local.qrySponsors = local.strSeminar.qrySponsors>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">Sponsors</h5>
				<div style="margin:10px 20px 2px 0;">
					<cfinclude template="/views/semwebCatalog/default/swProgramDetailsSpeakersCommon.cfm">
				</div>
				<br/>
			</div>
		</cfif>
			
		<cfif len(local.speakerBio)>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">Presenters</h5>
				<div style="margin:2px 20px 2px 0;">#local.speakerBio#</div>
				<br/>
			</div>	
		</cfif>
		
		<div class="tsAppBodyText">
			<h5 class="swPrimary">Originally Published</h5>
			<div style="margin:2px 20px 2px 0;">
				#DateFormat(local.strSeminar.qrySeminar.dateOrigPublished,"mmmm d, yyyy")#
			</div>
			<br/>
		</div>
		
		<!--- prereqs --->
		<cfif local.qryPreReqs.recordcount>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">Program Prerequisites</h5>
				<div style="margin:2px 20px 2px 0;">
					You must successfully complete the following prerequisites prior to beginning this program:<br/>
					<ul>
					<cfloop query="local.qryPreReqs">
						<li><a href="#attributes.event.getValue('mainurl')#&panel=showSWOD&seminarID=#local.qryPreReqs.seminarID#" class="swPrimary tsAppBodyText">#encodeForHTML(local.qryPreReqs.seminarName)#</a></li>
					</cfloop>
					</ul>
				</div>
				<br/>
			</div>
		</cfif>
		
		</cfoutput>
		<cfif local.qryOutline.recordcount>
			<cfquery name="local.qryTitlesInSem" dbtype="query">
				select distinct titleID from [local].qryOutline
			</cfquery>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">Program Titles and Supporting Materials</h5>
				<div style="margin:2px 20px 2px 0;">
					This program contains the following <cfif local.qryTitlesInSem.recordcount gt 1>titles and </cfif>components:<br/>
					<ul id="mainUL" class="sw-prgtitles sw-p-3 sw-m-0">
					<cfoutput query="local.qryOutline" group="titleID">
						<cfset local.currentFileGroup = "">
						<cfset local.mediaFilesShown = false>
						<cfset local.downloadFilesShown = false>
						<cfset local.pvrFilesShown = false>
						<cfif local.qryTitlesInSem.recordcount gt 1>
							<li type="a"><b>#local.qryOutline.titleName#</b><br/>
							<ul class="sw-p-3 sw-m-0">
						</cfif>
						<cfoutput>
							<!--- Determine file group for single title programs --->
							<cfset local.fileGroup = "">
							<cfif local.qryOutline.fileType eq "video" OR local.qryOutline.fileType eq "audio">
								<cfset local.fileGroup = "media">
							<cfelseif local.qryOutline.fileType eq "paper" AND local.qryOutline.fileTitle eq "Slides">
								<cfset local.fileGroup = "pvr">
							<cfelse>
								<cfset local.fileGroup = "download">
							</cfif>

							<!--- Show section header when file group changes --->
							<cfif local.currentFileGroup neq local.fileGroup>
								<cfset local.currentFileGroup = local.fileGroup>
								<cfif local.fileGroup eq "media" AND NOT local.mediaFilesShown>
									<li style="list-style:none;"><br/><b class="swPrimary">Media Files</b></li>
									<cfset local.mediaFilesShown = true>
								<cfelseif local.fileGroup eq "download" AND NOT local.downloadFilesShown>
									<li style="list-style:none;"><br/><b class="swPrimary">Downloadable Files</b></li>
									<cfset local.downloadFilesShown = true>
								<cfelseif local.fileGroup eq "pvr" AND NOT local.pvrFilesShown>
									<li style="list-style:none;"><br/><b class="swPrimary">PVR Images</b></li>
									<cfset local.pvrFilesShown = true>
								</cfif>
							</cfif>

							<cfif local.qryOutline.fileType eq "video">
								<li class="icon"><i class="bi bi-camera-reels swMuted"></i> #local.qryOutline.fileTitle#</li>
							<cfelseif local.qryOutline.fileType eq "audio">
								<li class="icon"><i class="bi bi-volume-down swMuted"></i> #local.qryOutline.fileTitle#</li>
							<cfelse>
								<cfset local.xmlFormats = XMLSearch(local.qryOutline.formatsAvailable,"/formats/format[not (@ext='flv' or @ext='swf' or @ext='pvr')]")>
								<cfif arraylen(local.xmlFormats)>
									<cfloop from="1" to="#arraylen(local.xmlFormats)#" index="local.typeNum">
										<li class="icon">
											<cfif StructKeyExists(local.faClassesFiles,local.xmlFormats[local.typeNum].xmlAttributes.ext)>
												<i class="#local.faClassesFiles[local.xmlFormats[local.typeNum].xmlAttributes.ext]# swMuted"></i>
											<cfelse>
												<i class="bi bi-file-earmark swMuted"></i>
											</cfif>
											#local.qryOutline.fileTitle#
										</li>
									</cfloop>
								<cfelse>
									<li class="icon"><i class="bi bi-file-earmark-text swMuted"></i> #local.qryOutline.fileTitle#</li>
								</cfif>
							</cfif>
						</cfoutput>
						<cfif local.qryTitlesInSem.recordcount gt 1>
							</ul>
							</li>
						</cfif>
					</cfoutput>
					</ul>
				</div>
				<br/>
			</div>
		</cfif>
		<cfoutput>
		<div class="tsAppBodyText">
			<h5 class="swPrimary">How To Attend</h5>
			<div style="margin:2px 20px 2px 0;">
				Join the self-paced program from your office, home, or hotel room using a computer and high speed 
				internet connection. You may start and stop the program at your convenience, continue where you left off, and review supporting materials as often as you like.
				<b>Please note: Internet Explorer is no longer a supported browser.</b> We recommend using Google Chrome, Mozilla Firefox or Safari for best results.
			</div>
			<br/>
		</div>
		<div class="tsAppBodyText">
			<h5 class="swPrimary">Technical Requirements</h5>
			<div style="margin:2px 20px 2px 0;">
				You may access this course on a computer or mobile device with high speed internet (iPhones require iOS 10 or higher). Recommended browsers are Google Chrome or Mozilla Firefox.
			</div>
			<br/>
		</div>
		<div class="tsAppBodyText">
			<h5 id="creditSection" class="swPrimary">Credit</h5>
			<div style="margin:2px 20px 2px 0;">
				<cfif local.strCredit.qryCreditDistinct.recordcount>
					If applicable, you may obtain credit in multiple jurisdictions simultaneously for this program (see pending/approved list below). 
					<cfif local.strSeminar.qrySeminar.offerCertificate>
						If electing credit for this program, registrants in jurisdictions not listed below will receive a 
						Certificate of Completion that may or may not meet credit requirements in other jurisdictions. 
					</cfif>
					Where applicable, credit will be only awarded to a paid registrant completing 
					all the requirements of the program as determined by the selected accreditation authority. 
					<br/><br/>
					<div style="border:1px solid ##DEDEDE;padding:0px;margin-left:20px;width:70%;">
						<span style="background-color:##DEDEDE;padding:2px;">&nbsp;<b>Click on jurisdiction for specific details</b>&nbsp;&nbsp;</span>
						<br/>
						<div style="padding:2px 20px 0 20px;">
						<cfloop query="local.strCredit.qryCreditDistinct">
							<a href="" onClick="return showCreditDetail(#local.strCredit.qryCreditDistinct.authorityID#);" class="swPrimary tsAppBodyText">#local.strCredit.qryCreditDistinct.authorityCode#</a><cfif local.strCredit.qryCreditDistinct.currentrow is not local.strCredit.qryCreditDistinct.recordcount>, </cfif>
						</cfloop>
						</div>
						<br/>
						<div style="padding:2px 20px 0 20px;" id="creditdetailbox"></div>
					</div>
				<cfelse>
					This program <i>has not been submitted</i> for credit in any jurisdiction. 
					<cfif local.strSeminar.qrySeminar.offerCertificate>
						Registrants will receive a Certificate of Attendance/Completion that may or may not meet credit requirements in various jurisdictions. 
					</cfif>
				</cfif>
			</div>
			<br/>
		</div>

		<cfif local.qrySWP.handlesOwnPayment is 0>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">Refund Policy</h5>
				<div style="margin:2px 20px 2px 0;">
					SeminarWeb and #local.semWeb.orgname# programs are non-refundable. 
				</div>
				<br/>
			</div>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">Privacy Statement</h5>
				<div style="margin:2px 20px 2px 0;">
					We respect and are committed to protecting your privacy. (<a href="javascript:sw_showPrivacyStatement();" class="swPrimary tsAppBodyText">Read Statement</a>) 
				</div>
				<br/>
			</div>
		</cfif>

		<cfif local.seminarSuggestions.recordcount>
			<div class="tsAppBodyText">
				<h5 class="swPrimary">You May Also Be Interested In...</h5>
				<div style="margin:2px 20px 2px 0;">
					<cfloop query="local.seminarSuggestions">
						<cfset local.otherPgmsFeaturedThumbImageFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.seminarSuggestions.featureImageOrgCode)#/#LCASE(local.seminarSuggestions.featureImageSiteCode)#/featuredimages/thumbnails/">
						<cfset local.otherPgmsFeaturedThumbImageRootPath = "/userassets/#LCASE(local.seminarSuggestions.featureImageOrgCode)#/#LCASE(local.seminarSuggestions.featureImageSiteCode)#/featuredimages/thumbnails/">
						<cfset local.thisProgramFeaturedImagePath = "">
						<cfif local.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.seminarSuggestions.featureImageID) AND fileExists("#local.otherPgmsFeaturedThumbImageFullRootPath##local.seminarSuggestions.featureImageID#-#local.seminarSuggestions.featureImageSizeID#.#local.seminarSuggestions.fileExtension#")>
							<cfset local.thisProgramFeaturedImagePath = "#local.otherPgmsFeaturedThumbImageRootPath##local.seminarSuggestions.featureImageID#-#local.seminarSuggestions.featureImageSizeID#.#local.seminarSuggestions.fileExtension#">
						<cfelseif local.seminarSuggestions.seminarType eq "On-Demand" and len(attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWODFeaturedImagePath)>
							<cfset local.thisProgramFeaturedImagePath = attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWODFeaturedImagePath>
						<cfelseif local.seminarSuggestions.seminarType eq "Webinar" and len(attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWLFeaturedImagePath)>
							<cfset local.thisProgramFeaturedImagePath = attributes.data.defaultFeaturedImagePathsOtherProgramsStr.defaultSWLFeaturedImagePath>
						</cfif>
						<cfif local.seminarSuggestions.seminarType eq "On-Demand">
							<cfset local.programLink = "?pg=semwebCatalog&panel=showSWOD&seminarid=#local.seminarSuggestions.seminarID#">
						<cfelse>
							<cfset local.programLink = "?pg=semwebCatalog&panel=showLive&seminarid=#local.seminarSuggestions.seminarID#">
						</cfif>

						<div class="sw-d-flex sw-mb-3">
							<cfif len(local.thisProgramFeaturedImagePath)>
								<div class="sw-mr-2">
									<img src="#local.thisProgramFeaturedImagePath#" style="width:85px;height:85px;" alt="">
								</div>
							</cfif>
							<div>
								<div class="sw-d-flex">
									<cfif local.seminarSuggestions.seminarType eq "On-Demand">
										<div class="swOnDemand sw-font-size-sm sw-text-uppercase"><i class="bi bi-play-circle" aria-hidden="true"></i> #attributes.data.qrySWP.brandSWODTab#</div>
									<cfelse>
										<div class="swWebinar sw-font-size-sm sw-text-uppercase"><i class="bi bi-laptop" aria-hidden="true"></i> #attributes.data.qrySWP.brandSWLTab#</div>
									</cfif>
									<span class="sw-font-size-sm sw-ml-3" title="<cfif local.seminarSuggestions.seminarType eq "On-Demand">Published on<cfelse>Being Held On </cfif> #dateformat(local.seminarSuggestions.DatePublished, "mmm dd")#">
										<i class="bi bi-calendar-fill" aria-hidden="true"></i> #dateformat(local.seminarSuggestions.DatePublished, "mmm dd")#
									</span>
								</div>
								<div class="sw-mt-1">
									<b>
										<a href="#local.programLink#" class="swPrimary tsAppBodyText" style="text-decoration:none;" target="_blank">
											#encodeForHTML(local.seminarSuggestions.seminarName)#<cfif len(local.seminarSuggestions.seminarSubTitle)>: #encodeForHTML(local.seminarSuggestions.seminarSubTitle)#</cfif>
										</a>
									</b>
								</div>
							</div>
						</div>
					</cfloop>
				</div>
			</div>
		</cfif>

		<br clear="all">
	</div>
</div>
</cfoutput>