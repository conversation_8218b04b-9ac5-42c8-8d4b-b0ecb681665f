USE membercentral
GO

-- Add orgIdentityID column to mp_profiles table
ALTER TABLE dbo.mp_profiles ADD orgIdentityID int NULL;
GO


-- Add foreign key constraint for orgIdentityID (assuming dbo.orgIdentities exists)
ALTER TABLE dbo.mp_profiles
ADD CONSTRAINT FK_mp_profiles_orgIdentities
FOREIGN KEY (orgIdentityID) REFERENCES dbo.orgIdentities(orgIdentityID);
GO
-- update the new column orgIdentityID with the default org identity for that site
UPDATE mp
SET mp.orgIdentityID = o.defaultOrgIdentityID
FROM dbo.mp_profiles as mp
INNER JOIN dbo.sites as s ON s.siteID = mp.siteID
INNER JOIN dbo.organizations as o ON o.orgID = s.orgID
WHERE mp.orgIdentityID IS NULL
AND o.defaultOrgIdentityID IS NOT NULL;
GO
-- To make the column not null
ALTER TABLE dbo.mp_profiles ALTER COLUMN orgIdentityID int NOT NULL;
GO

-- Add orgIdentityID column to tr_invoiceProfiles table
ALTER TABLE dbo.tr_invoiceProfiles ADD orgIdentityID int NULL;
GO

-- Add foreign key constraint for orgIdentityID (assuming dbo.orgIdentities exists)
ALTER TABLE dbo.tr_invoiceProfiles
ADD CONSTRAINT FK_tr_invoiceProfiles_orgIdentities
FOREIGN KEY (orgIdentityID) REFERENCES dbo.orgIdentities(orgIdentityID);
GO
-- update the new column orgIdentityID with the default org identity for that org
UPDATE ip
SET ip.orgIdentityID = o.defaultOrgIdentityID
FROM dbo.tr_invoiceProfiles as ip
INNER JOIN dbo.organizations as o ON o.orgID = ip.orgID
WHERE ip.orgIdentityID IS NULL
AND o.defaultOrgIdentityID IS NOT NULL;
GO
--make the column not null
ALTER TABLE dbo.tr_invoiceProfiles ALTER COLUMN orgIdentityID int NOT NULL;
GO

ALTER PROC dbo.ams_getOrgIdentityUsage
@orgID INT,
@orgIdentityID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	-- keep this in sync with ams_isOrgIdentityInUse
	DECLARE @resourceTypes TABLE (orgIdentityUsage varchar(200), usageCount int);

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'List Servers', COUNT(listID)
	FROM dbo.lists_lists
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Default for Site', COUNT(siteID)
	FROM dbo.sites
	WHERE orgID = @orgID
	AND defaultOrgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Login for Site', COUNT(siteID)
	FROM dbo.sites
	WHERE orgID = @orgID
	AND loginOrgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Publications' AS usageType, COUNT(publicationID) AS usageCount
	FROM dbo.pub_publications
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Email Blasts' AS usageType, COUNT(blastID) AS usageCount
	FROM dbo.email_emailBlasts
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Default for Org', COUNT(orgID)
	FROM dbo.organizations
	WHERE orgID = @orgID
	AND defaultOrgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'SeminarWeb' AS usageType, COUNT(participantID) AS usageCount
	FROM seminarWeb.dbo.tblParticipants
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Consent Lists' AS usageType, COUNT(consentListID) AS usageCount
	FROM platformMail.dbo.email_consentLists
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Email Messages' AS usageType, COUNT(messageID) AS usageCount
	FROM platformMail.dbo.email_messages
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'SeminarWeb Complete Seminar Reminders' AS usageType, COUNT(itemID) AS usageCount
	FROM platformQueue.dbo.queue_completeSeminarReminder
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Store' AS usageType, COUNT(storeID) AS usageCount
	FROM dbo.store
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Merchant Profile' AS usageType, COUNT(profileID) AS usageCount
	FROM dbo.mp_profiles
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Invoice Profile' AS usageType, COUNT(profileID) AS usageCount
	FROM dbo.tr_invoiceProfiles
	WHERE orgIdentityID = @orgIdentityID;

	DELETE FROM @resourceTypes
	WHERE usageCount = 0;

	SELECT orgIdentityUsage as usageType, usageCount
	FROM @resourceTypes
	ORDER BY usageCount desc, orgIdentityUsage;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER FUNCTION dbo.ams_isOrgIdentityInUse (@orgID INT, @orgIdentityID INT)
RETURNS bit
AS
BEGIN

	DECLARE @inUse bit = 0, @count int;

	set @count = 0;
	select @count = count(listID) FROM dbo.lists_lists WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(siteID) FROM dbo.sites WHERE orgID = @orgID and loginOrgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END
	
	set @count = 0;
	select @count = count(siteID) FROM dbo.sites WHERE orgID = @orgID and defaultOrgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(publicationID) FROM dbo.pub_publications WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(blastID) FROM dbo.email_emailBlasts WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(orgID) FROM dbo.organizations WHERE orgID = @orgID AND defaultOrgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(participantID) FROM seminarWeb.dbo.tblParticipants WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(consentListID) FROM platformMail.dbo.email_consentLists WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END
	
	set @count = 0;
	select @count = count(messageID) FROM platformMail.dbo.email_messages WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(itemID) FROM platformQueue.dbo.queue_completeSeminarReminder WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(storeID) FROM dbo.store WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(profileID) FROM dbo.mp_profiles WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	set @count = 0;
	select @count = count(profileID) FROM dbo.tr_invoiceProfiles WHERE orgIdentityID = @orgIdentityID;
	IF @count > 0 BEGIN
		SET @inUse = 1;
		GOTO on_done;
	END

	on_done:
	RETURN @inUse;

END
GO

ALTER PROCEDURE dbo.mp_insertProfile
@siteID int,
@gatewayID int,
@profileName varchar(100),
@tabTitle varchar(100),
@profileCode varchar(20),
@transLabel varchar(25),
@gatewayUsername varchar(50),
@gatewayPassword varchar(75),
@allowPayments bit,
@allowRefunds bit,
@allowRefundsFromAnyProfile bit,
@GLAccountID int,
@gatewayMerchantId varchar(50),
@allowPayInvoicesOnline bit,
@bankAccountName varchar(200),
@maxFailedAutoAttempts int,
@daysBetweenAutoAttempts int,
@minDaysFailedCleanup int,
@orgIdentityID int,
@recordedByMemberID int,
@profileID int OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	declare @sysCreatedContentResourceTypeID int, @PayInstrContentID int, @PayInstrSiteResourceID int, 
		@profileUID uniqueidentifier, @orgID int, @environmentName varchar(12);

	SET @profileID = null;
	SET @profileUID = NEWID();

	SELECT @environmentName = tier 
	FROM dbo.fn_getServerSettings();

	-- enforce unique authorize gateway credentials in prod
	IF @gatewayID = 10 AND @environmentName = 'Production' AND EXISTS (select profileID from dbo.mp_profiles where gatewayID = @gatewayID and [status] <> 'D' AND gatewayUsername + gatewayPassword = @gatewayUsername + @gatewayPassword)
		RAISERROR('Authorize Credentials already in use',16,1);

	BEGIN TRAN;
		INSERT INTO dbo.mp_profiles (siteID, gatewayID, profileName, tabTitle, profileCode, gatewayUsername, 
			gatewayPassword, gatewayMerchantId, allowPayments, allowRefunds, allowRefundsFromAnyProfile, 
			GLAccountID, [status], allowPayInvoicesOnline, bankAccountName, maxFailedAutoAttempts, 
			daysBetweenAutoAttempts, minDaysFailedCleanup, orgIdentityID, transactionLabel, [uid], frontEndOrderBy)
		VALUES (@siteID, @gatewayID, @profileName, @tabTitle, @profileCode, @gatewayUsername, @gatewayPassword, 
			@gatewayMerchantId, @allowPayments, @allowRefunds, @allowRefundsFromAnyProfile, nullif(@GLAccountID,0), 
			'A', @allowPayInvoicesOnline, @bankAccountName, nullif(@maxFailedAutoAttempts,0), 
			nullif(@daysBetweenAutoAttempts,0), @minDaysFailedCleanup, @orgIdentityID, @transLabel, @profileUID, 999);

		SELECT @profileID = SCOPE_IDENTITY();

		IF @gatewayID = 11 BEGIN
			select @sysCreatedContentResourceTypeID = dbo.fn_getResourceTypeId('SystemCreatedContent');

			EXEC dbo.cms_createContentObject @siteID=@siteID, @resourceTypeID=@sysCreatedContentResourceTypeID, 
				@siteResourceStatusID=1, @isHTML=1, @languageID=1, 
				@isActive=1, @contentTitle=null, @contentDesc=null, @rawContent='', @memberID=NULL,
				@contentID=@PayInstrContentID OUTPUT, @siteResourceID=@PayInstrSiteResourceID OUTPUT;

			UPDATE dbo.mp_profiles
			SET paymentInstructionsContentID = @PayInstrContentID
			WHERE profileID = @profileID;
		END

		EXEC dbo.mp_reorderProfiles @siteID=@siteID;

		SELECT @orgID = s.orgID
		FROM dbo.sites as s WHERE s.siteID = @siteID;

		INSERT INTO platformQueue.dbo.queue_mongo (msgjson)
			SELECT '{ "c":"auditLog", "d": {
				"AUDITCODE":"PP",
				"ORGID":' + CAST(@orgID AS VARCHAR(10)) + ',
				"SITEID":' + CAST(@siteID AS VARCHAR(10)) + ',
				"ACTORMEMBERID":' + CAST(@recordedByMemberID AS VARCHAR(20)) + ',
				"ACTIONDATE":"' + CONVERT(VARCHAR(20),GETDATE(),120) + '",
				"MESSAGE":"' + REPLACE(memberCentral.dbo.fn_cleanInvalidXMLChars('' + pp.profileName + 'has been created'),'"','\"') + '" } }'
			FROM dbo.mp_profiles AS pp
			WHERE profileID = @profileID;
	COMMIT TRAN;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

USE datatransfer
GO

DROP TABLE dbo.sync_tr_invoiceProfiles;
GO

CREATE TABLE dbo.sync_tr_invoiceProfiles (orgCode varchar(10), orgID int, profileID int, profileName varchar(50), [status] char(1), imageExt varchar(5), 
	enableAutoPay bit, enforcePayOldest bit, notifyEmail varchar(100), allowPartialPayment bit, numDaysDelinquent int, enableProcessingFeeDonation bit,
	processFeeDonationDefaultSelect bit, solicitationMessageID int, orgIdentityOrgName varchar(100), useOrgIdentityID int, finalAction char(1));
GO

USE membercentral
GO

ALTER PROC dbo.tr_exportInvoiceProfilesStructure
@siteID int,
@exportPath varchar(400)

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	DECLARE @orgID int, @orgCode varchar(10), @siteCode varchar(10), @cmd varchar(4000), @svrName varchar(40);

	SELECT @siteCode = s.siteCode, @orgID = o.orgID, @orgCode = o.orgCode
	FROM dbo.sites AS s
	INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
	WHERE s.siteID = @siteID;
	
	SET @svrName = CAST(SERVERPROPERTY('ServerName') AS VARCHAR(40));

	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfiles WHERE orgCode = @orgCode;
	DELETE FROM datatransfer.dbo.sync_tr_solicitationMessages WHERE siteCode = @siteCode;
	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles WHERE orgCode = @orgCode;

	INSERT INTO datatransfer.dbo.sync_tr_invoiceProfiles (orgCode, orgID, profileID, profileName, [status], imageExt, 
		enableAutoPay, enforcePayOldest, notifyEmail, allowPartialPayment, numDaysDelinquent, enableProcessingFeeDonation, 
		processFeeDonationDefaultSelect, solicitationMessageID, orgIdentityOrgName)
	SELECT @orgCode, @orgID, ip.profileID, ip.profileName, 'A', ip.imageExt, ip.enableAutoPay, ip.enforcePayOldest, isnull(ip.notifyEmail,''), 
		ip.allowPartialPayment, isnull(ip.numDaysDelinquent,0), ip.enableProcessingFeeDonation, ip.processFeeDonationDefaultSelect, 
		ip.solicitationMessageID, oi.organizationName
	FROM dbo.tr_invoiceProfiles AS ip
	INNER JOIN dbo.orgIdentities AS oi ON oi.orgID = @orgID
		AND oi.orgIdentityID = ip.orgIdentityID
	WHERE ip.orgID = @orgID
	AND ip.[status] = 'A'
	ORDER BY ip.profilename;

	INSERT INTO datatransfer.dbo.sync_tr_solicitationMessages (siteCode, siteID, messageID, title, message)
	SELECT @siteCode, @siteID, messageID, title, message
	FROM dbo.tr_solicitationMessages
	WHERE siteID = @siteID;

	INSERT INTO datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles (orgCode, orgID, invoiceProfileID, merchantProfileID, profileCode)
	select distinct @orgCode, @orgID, ipmp.invoiceProfileID, mp.profileID, mp.profileCode
	from dbo.tr_invoiceProfilesMerchantProfiles as ipmp
	inner join dbo.mp_profiles as mp on mp.profileID = ipmp.merchantProfileID
	inner join dbo.sites as s on s.siteID = mp.siteID
	where s.orgID = @orgID
	and mp.[status] = 'A';

	-- export to file
	SET @cmd = 'bcp "SELECT orgCode, CAST(NULL AS INT) AS orgID, profileID, profileName, [status], imageExt, enableAutoPay, enforcePayOldest, notifyEmail, allowPartialPayment, numDaysDelinquent, enableProcessingFeeDonation, processFeeDonationDefaultSelect, solicitationMessageID, orgIdentityOrgName, useOrgIdentityID, finalAction FROM datatransfer.dbo.sync_tr_invoiceProfiles WHERE orgCode = ''' + @orgCode + '''" queryout "'+@exportPath+'sync_tr_invoiceProfiles.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp "SELECT siteCode, CAST(NULL AS INT) AS siteID, messageID, title, message, useMessageID, finalAction FROM datatransfer.dbo.sync_tr_solicitationMessages WHERE siteCode = ''' + @siteCode + '''" queryout "'+@exportPath+'sync_tr_solicitationMessages.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp "SELECT orgCode, CAST(NULL AS INT) AS orgID, invoiceProfileID, merchantProfileID, profileCode, useInvoiceProfileID, useMerchantProfileID, finalAction FROM datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles WHERE orgCode = ''' + @orgCode + '''" queryout "'+@exportPath+'sync_tr_invoiceProfilesMerchantProfiles.bcp" -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	-- clear sync tables
	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfiles WHERE orgID = @orgID;
	DELETE FROM datatransfer.dbo.sync_tr_solicitationMessages WHERE siteID = @siteID;
	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles WHERE orgID = @orgID;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.tr_prepareInvoiceProfilesImport
@siteID int,
@pathToImport varchar(400),
@importResult XML OUTPUT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgInvoiceProfilesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpOrgInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgUpdateInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgUpdateInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgDeleteInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteInvoiceProfilesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpOrgDeleteInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSiteSolicitationMessages') IS NOT NULL
		DROP TABLE #tmpSiteSolicitationMessages;

	CREATE TABLE #tblImportErrors (rowID int IDENTITY(1,1), msg varchar(600), errorCode varchar(20));
	CREATE TABLE #tmpOrgInvoiceProfiles (profileID int, profileName varchar(50), enableAutoPay bit, enforcePayOldest bit, 
		notifyEmail varchar(100), allowPartialPayment bit, numDaysDelinquent int, enableProcessingFeeDonation bit, 
		processFeeDonationDefaultSelect bit, solicitationMessageID int, orgIdentityOrgName varchar(100));
	CREATE TABLE #tmpOrgInvoiceProfilesMerchantProfiles (invoiceProfileID int, merchantProfileID int, profileCode varchar(20), profileName varchar(100));
	CREATE TABLE #tmpOrgUpdateInvoiceProfiles (profileName varchar(50));
	CREATE TABLE #tmpOrgDeleteInvoiceProfiles (invoiceProfileID int, profileName varchar(50));
	CREATE TABLE #tmpOrgDeleteInvoiceProfilesMerchantProfiles (invoiceProfileID int, merchantProfileID int, profileCode varchar(20), profileName varchar(100));
	CREATE TABLE #tmpSiteSolicitationMessages (messageID int, title varchar(100), message varchar(800));

	DECLARE @orgID int, @orgCode varchar(10), @siteCode varchar(10), @cmd varchar(400), @svrName varchar(40);
	
	SELECT @siteCode = s.siteCode, @orgCode = o.orgCode, @orgID = o.orgID
	FROM dbo.sites AS s
	INNER JOIN dbo.organizations AS o ON o.orgID = s.orgID
	WHERE s.siteID = @siteID;

	SET @svrName = CAST(SERVERPROPERTY('ServerName') AS varchar(40));

	-- ensure files are present
	IF dbo.fn_FileExists(@pathToImport + 'sync_tr_invoiceProfiles.bcp') = 0 
		OR dbo.fn_FileExists(@pathToImport + 'sync_tr_solicitationMessages.bcp') = 0 
		OR dbo.fn_FileExists(@pathToImport + 'sync_tr_invoiceProfilesMerchantProfiles.bcp') = 0 BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) VALUES ('Required files in the backup file is missing.', 'FILEMISSING');
		GOTO on_done;
	END

	-- delete org sync rows
	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfiles WHERE orgCode = @orgCode;
	DELETE FROM datatransfer.dbo.sync_tr_solicitationMessages WHERE siteCode = @siteCode;
	DELETE FROM datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles WHERE orgCode = @orgCode;

	-- import data
	SET @cmd = 'bcp datatransfer.dbo.sync_tr_invoiceProfiles in ' + @pathToImport + 'sync_tr_invoiceProfiles.bcp -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp datatransfer.dbo.sync_tr_solicitationMessages in ' + @pathToImport + 'sync_tr_solicitationMessages.bcp -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	SET @cmd = 'bcp datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles in ' + @pathToImport + 'sync_tr_invoiceProfilesMerchantProfiles.bcp -t'+CHAR(7)+' -w -T -S' + @svrName;
	EXEC master..xp_cmdshell @cmd, NO_OUTPUT;

	-- set orgID/siteID in datatransfer tables. we do this because orgID on one tier may not be the same as another tier
	UPDATE datatransfer.dbo.sync_tr_invoiceProfiles SET orgID = @orgID WHERE orgCode = @orgCode;
	UPDATE datatransfer.dbo.sync_tr_solicitationMessages SET siteID = @siteID WHERE siteCode = @siteCode;
	UPDATE datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles SET orgID = @orgID WHERE orgCode = @orgCode;

	INSERT INTO #tmpOrgInvoiceProfiles (profileID, profileName, enableAutoPay, enforcePayOldest, notifyEmail, allowPartialPayment, 
		numDaysDelinquent, enableProcessingFeeDonation, processFeeDonationDefaultSelect, solicitationMessageID, orgIdentityOrgName)
	SELECT ip.profileID, ip.profileName, ip.enableAutoPay, ip.enforcePayOldest, isnull(ip.notifyEmail,''), 
		ip.allowPartialPayment, isnull(ip.numDaysDelinquent,0), ip.enableProcessingFeeDonation, ip.processFeeDonationDefaultSelect, 
		ip.solicitationMessageID, oi.organizationName
	FROM dbo.tr_invoiceProfiles AS ip
	INNER JOIN dbo.orgIdentities AS oi ON oi.orgID = @orgID
		AND oi.orgIdentityID = ip.orgIdentityID
	WHERE ip.orgID = @orgID
	AND ip.[status] = 'A'
	ORDER BY ip.profilename;

	INSERT INTO #tmpOrgInvoiceProfilesMerchantProfiles (invoiceProfileID, merchantProfileID, profileCode, profileName)
	select distinct ipmp.invoiceProfileID, mp.profileID, mp.profileCode, mp.profileName
	from dbo.tr_invoiceProfilesMerchantProfiles as ipmp
	inner join dbo.mp_profiles as mp on mp.profileID = ipmp.merchantProfileID
	inner join dbo.sites as s on s.siteID = mp.siteID
	where s.orgID = @orgID
	and mp.[status] = 'A';

	INSERT INTO #tmpSiteSolicitationMessages (messageID, title, message)
	SELECT messageID, title, message
	FROM dbo.tr_solicitationMessages
	WHERE siteID = @siteID;

	-- update useOrgIdentityID
	UPDATE sip
	SET sip.useOrgIdentityID = oi.orgIdentityID
	FROM dataTransfer.dbo.sync_tr_invoiceProfiles AS sip
	INNER JOIN dbo.orgIdentities AS oi ON oi.orgID = @orgID
		AND oi.organizationName = sip.orgIdentityOrgName
	WHERE sip.orgID = @orgID;

	IF EXISTS (select 1 from dataTransfer.dbo.sync_tr_invoiceProfiles where orgID = @orgID and useOrgIdentityID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) 
		select 'Org Identity "' + orgIdentityOrgName + '" does not match any existing Org Identities.', 'INVALIDORGIDENTITY'
		from dataTransfer.dbo.sync_tr_invoiceProfiles 
		where orgID = @orgID 
		and useOrgIdentityID is null;

		GOTO on_done;
	END

	-- update useMerchantProfileID
	update sipmp
	set sipmp.useMerchantProfileID = mp.profileID
	from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp
	inner join dbo.sites as s on s.orgID = @orgID
	inner join dbo.mp_profiles as mp on mp.siteID = s.siteID and mp.profileCode = sipmp.profileCode
	where sipmp.orgID = @orgID;

	IF EXISTS (select 1 from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles where orgID = @orgID and useMerchantProfileID is null) BEGIN
		INSERT INTO #tblImportErrors (msg, errorCode) 
		select 'Profile Code "' + profileCode + '" does not match any existing Merchant Profile Codes', 'INVALIDMP'
		from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles 
		where orgID = @orgID 
		and useMerchantProfileID is null;

		GOTO on_done;
	END

	-- solicitation messages
	UPDATE spfm
	SET spfm.useMessageID = m.messageID
	FROM dataTransfer.dbo.sync_tr_solicitationMessages AS spfm
	INNER JOIN #tmpSiteSolicitationMessages AS m ON m.message = spfm.message
		AND m.title = spfm.title
	WHERE spfm.siteID = @siteID;

	-- new solicitation messages
	update dataTransfer.dbo.sync_tr_solicitationMessages
	set finalAction = 'A'
	where siteID = @siteID
	and useMessageID is null;

	-- remove invoice profiles
	INSERT INTO #tmpOrgDeleteInvoiceProfiles (invoiceProfileID, profileName)
	select ip.profileID, ip.profileName
	from #tmpOrgInvoiceProfiles as ip
	left outer join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileName = ip.profileName
	where sip.profileID is null;

	-- invoice profiles to be deleted is in use
	INSERT INTO #tblImportErrors (msg, errorCode) 
	select distinct 'Invoice Profile "' + profileName + '" is linked to GL Accounts', 'IPINGLUSE'
	from #tmpOrgDeleteInvoiceProfiles as tmp
	inner join dbo.tr_GLAccounts as g on g.orgID = @orgID and g.invoiceProfileID = tmp.invoiceProfileID;

	INSERT INTO #tblImportErrors (msg, errorCode) 
	select distinct 'Invoice Profile "' + profileName + '" is linked to Invoices', 'IPINUSE'
	from #tmpOrgDeleteInvoiceProfiles as tmp
	inner join dbo.tr_invoices as i on i.orgID = @orgID and i.invoiceProfileID = tmp.invoiceProfileID;

	INSERT INTO #tblImportErrors (msg, errorCode) 
	select distinct 'Invoice Profile "' + profileName + '" is linked to Group Assignment Conditions', 'IPINVGCUSE'
	from #tmpOrgDeleteInvoiceProfiles as tmp
	where exists (
		select c.conditionID 
		from dbo.ams_virtualGroupConditions as c 
		inner join dbo.ams_virtualGroupConditionValues as cv on cv.conditionID = c.conditionID
		inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'acctInvProf'
		where cv.conditionValue = cast(tmp.invoiceProfileID as varchar(20)));

	IF EXISTS (select 1 from #tblImportErrors) 
		GOTO on_done;

	-- update useInvoiceProfileID
	update sipmp
	set sipmp.useInvoiceProfileID = tmp.profileID
	from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp
	inner join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileID = sipmp.invoiceProfileID
	inner join #tmpOrgInvoiceProfiles as tmp on tmp.profileName = sip.profileName
	where sipmp.orgID = @orgID;


	-- new invoice profiles
	update sip
	set sip.finalAction = 'A'
	from dataTransfer.dbo.sync_tr_invoiceProfiles as sip
	left outer join #tmpOrgInvoiceProfiles as tmp on tmp.profileName = sip.profileName
	where sip.orgID = @orgID
	and tmp.profileID is null;

	update sipmp
	set sipmp.finalAction = 'A'
	from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp
	inner join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID 
		and sip.profileID = sipmp.invoiceProfileID
		and sip.finalAction = 'A'
	where sipmp.orgID = @orgID
	and sipmp.finalAction is null;

	-- update invoice profiles
	INSERT INTO #tmpOrgUpdateInvoiceProfiles (profileName)
	select distinct profileName
	from (
		select sip.profileName, sip.enableAutoPay, sip.enforcePayOldest, sip.notifyEmail, sip.allowPartialPayment, sip.numDaysDelinquent, sip.enableProcessingFeeDonation, 
			sip.processFeeDonationDefaultSelect, spfm.title, spfm.message, sip.orgIdentityOrgName
		from dataTransfer.dbo.sync_tr_invoiceProfiles as sip
		left outer join datatransfer.dbo.sync_tr_solicitationMessages as spfm on spfm.siteID = @siteID
			and spfm.messageID = sip.solicitationMessageID
		where sip.orgID = @orgID
		and sip.finalAction is null
			except
		select tmp.profileName, tmp.enableAutoPay, tmp.enforcePayOldest, tmp.notifyEmail, tmp.allowPartialPayment, tmp.numDaysDelinquent, tmp.enableProcessingFeeDonation, 
			tmp.processFeeDonationDefaultSelect, pfm.title, pfm.message, tmp.orgIdentityOrgName
		from #tmpOrgInvoiceProfiles as tmp
		inner join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileName = tmp.profileName
		left outer join #tmpSiteSolicitationMessages as pfm on pfm.messageID = tmp.solicitationMessageID
	) tmp;

	-- update action
	update sip
	set sip.finalAction = 'C'
	from dataTransfer.dbo.sync_tr_invoiceProfiles as sip
	inner join #tmpOrgUpdateInvoiceProfiles as tmp on tmp.profileName = sip.profileName
	where sip.orgID = @orgID
	and sip.finalAction is null;

	-- new invoice merchant profiles
	update sipmp
	set sipmp.finalAction = 'A'
	from dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp
	inner join #tmpOrgInvoiceProfiles as tmp on tmp.profileID = sipmp.useInvoiceProfileID
	where sipmp.orgID = @orgID
	and sipmp.finalAction is null
	and not exists (select 1 from #tmpOrgInvoiceProfilesMerchantProfiles where invoiceProfileID = sipmp.useInvoiceProfileID and merchantProfileID = sipmp.useMerchantProfileID)

	-- update action
	update sip
	set sip.finalAction = 'C'
	from dataTransfer.dbo.sync_tr_invoiceProfiles as sip
	inner join dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp on sipmp.orgID = @orgID and sipmp.invoiceProfileID = sip.profileID
	where sip.orgID = @orgID
	and sipmp.finalAction = 'A'
	and sip.finalAction is null;

	-- remove invoice profiles merchant profiles
	INSERT INTO #tmpOrgDeleteInvoiceProfilesMerchantProfiles (invoiceProfileID, merchantProfileID, profileCode, profileName)
	select ipmp.invoiceProfileID, ipmp.merchantProfileID, ipmp.profileCode, ipmp.profileName
	from #tmpOrgInvoiceProfilesMerchantProfiles as ipmp
	inner join #tmpOrgInvoiceProfiles as tmp on tmp.profileID = ipmp.invoiceProfileID
	inner join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileName = tmp.profileName
	left outer join dataTransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp on sipmp.orgID = @orgID 
		and sipmp.useInvoiceProfileID = ipmp.invoiceProfileID
		and sipmp.useMerchantProfileID = ipmp.merchantProfileID
	where sipmp.invoiceProfileID is null;

	-- update action
	update sip
	set sip.finalAction = 'C'
	from dataTransfer.dbo.sync_tr_invoiceProfiles as sip
	inner join #tmpOrgInvoiceProfiles as tmp on tmp.profileName = sip.profileName
	inner join #tmpOrgDeleteInvoiceProfilesMerchantProfiles as tmpD on tmpD.invoiceProfileID = tmp.profileID
	where sip.orgID = @orgID
	and sip.finalAction is null;
	
	on_done:
	-- return the xml results
	select @importResult = (
		select getdate() as "@date", 

			isnull((select distinct profileName as "@profilename"
			from dataTransfer.dbo.sync_tr_invoiceProfiles
			where orgID = @orgID
			and finalAction = 'A'
			FOR XML path('invoiceprofile'), root('newinvoiceprofiles'), type),'<newinvoiceprofiles/>'),

			isnull((select distinct profileName as "@profilename"
			from dataTransfer.dbo.sync_tr_invoiceProfiles
			where orgID = @orgID
			and finalAction = 'C'
			FOR XML path('invoiceprofile'), root('updateinvoiceprofiles'), type),'<updateinvoiceprofiles/>'),

			isnull((select distinct title as "@title", message as "@message"
			from dataTransfer.dbo.sync_tr_solicitationMessages
			where siteID = @siteID
			and finalAction = 'A'
			FOR XML path('solicitationmsg'), root('newsolicitationmsgs'), type),'<newsolicitationmsgs/>'),

			isnull((select distinct invoiceProfileID as "@invoiceprofileid", profileName as "@profilename"
			from #tmpOrgDeleteInvoiceProfiles
			FOR XML path('invoiceprofile'), root('removeinvoiceprofiles'), type),'<removeinvoiceprofiles/>'),

			isnull((select distinct invoiceProfileID as "@invoiceprofileid", merchantProfileID as "@merchantprofileid", 
				profileCode as "@profilecode", profileName as "@profilename"
			from #tmpOrgDeleteInvoiceProfilesMerchantProfiles
			FOR XML path('ipmp'), root('removeinvoiceprofilemerchantprofiles'), type),'<removeinvoiceprofilemerchantprofiles/>'),
			
			isnull((select dbo.fn_RegExReplace(isnull(msg,''),'[^\x20-\x7E]','') as "@msg", errorCode as "@errorcode"
			from #tblImportErrors
			order by msg
			FOR XML path('error'), root('errors'), type),'<errors/>')

		for xml path('import'), TYPE);

	IF OBJECT_ID('tempdb..#tblImportErrors') IS NOT NULL
		DROP TABLE #tblImportErrors;
	IF OBJECT_ID('tempdb..#tmpOrgInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgInvoiceProfilesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpOrgInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgUpdateInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgUpdateInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteInvoiceProfiles') IS NOT NULL
		DROP TABLE #tmpOrgDeleteInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpOrgDeleteInvoiceProfilesMerchantProfiles') IS NOT NULL
		DROP TABLE #tmpOrgDeleteInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSiteSolicitationMessages') IS NOT NULL
		DROP TABLE #tmpSiteSolicitationMessages;
	
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO

ALTER PROC dbo.tr_importInvoiceProfiles
@siteID int,
@recordedByMemberID int

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	IF OBJECT_ID('tempdb..#tmpInvoiceProfiles') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpInvoiceProfilesMerchantProfiles') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSolicitationMessages') IS NOT NULL 
		DROP TABLE #tmpSolicitationMessages;
	IF OBJECT_ID('tempdb..#tmpDeleteInvoiceProfiles') IS NOT NULL 
		DROP TABLE #tmpDeleteInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpDeleteInvoiceProfilesMerchantProfiles') IS NOT NULL 
		DROP TABLE #tmpDeleteInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpInvoiceProfileUpdates') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfileUpdates;
	
	CREATE TABLE #tmpInvoiceProfiles (syncInvoiceProfileID int, profileName varchar(50), enableAutoPay bit, enforcePayOldest bit, 
		notifyEmail varchar(100), allowPartialPayment bit, numDaysDelinquent int, enableProcessingFeeDonation bit, 
		processFeeDonationDefaultSelect bit, syncSolicitationMessageID int, orgIdentityID int, finalAction char(1));
	CREATE TABLE #tmpInvoiceProfilesMerchantProfiles (syncInvoiceProfileID int, useMerchantProfileID int, useInvoiceProfileID int);
	CREATE TABLE #tmpSolicitationMessages (syncSolicitationMessageID int, useMessageID int, title varchar(100), message varchar(800), finalAction char(1));
	CREATE TABLE #tmpDeleteInvoiceProfiles (invoiceProfileID int);
	CREATE TABLE #tmpDeleteInvoiceProfilesMerchantProfiles (linkID int);
	CREATE TABLE #tmpInvoiceProfileUpdates (invoiceProfileID int, currentNumDaysDelinquent int, newNumDaysDelinquent int);

	DECLARE @orgID int, @ins_delinquent int, @ins_closed int;
	select @orgID = dbo.fn_getOrgIDFromSiteID(@siteID);
	select @ins_delinquent = statusID from dbo.tr_invoiceStatuses where [status] = 'Delinquent';
	select @ins_closed = statusID from dbo.tr_invoiceStatuses where [status] = 'Closed';

	INSERT INTO #tmpInvoiceProfiles (syncInvoiceProfileID, profileName, enableAutoPay, enforcePayOldest, notifyEmail, allowPartialPayment, 
		numDaysDelinquent, enableProcessingFeeDonation, processFeeDonationDefaultSelect, syncSolicitationMessageID, orgIdentityID, finalAction)
	select distinct profileID, profileName, enableAutoPay, enforcePayOldest, notifyEmail, allowPartialPayment, numDaysDelinquent, 
		enableProcessingFeeDonation, processFeeDonationDefaultSelect, solicitationMessageID, useOrgIdentityID, finalAction
	from datatransfer.dbo.sync_tr_invoiceProfiles
	where orgID = @orgID
	and finalAction in ('A','C');

	INSERT INTO #tmpInvoiceProfilesMerchantProfiles (syncInvoiceProfileID, useMerchantProfileID, useInvoiceProfileID)
	select distinct sipmp.invoiceProfileID, sipmp.useMerchantProfileID, sipmp.useInvoiceProfileID
	from datatransfer.dbo.sync_tr_invoiceProfilesMerchantProfiles as sipmp
	inner join datatransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileID = sipmp.invoiceProfileID
	where sipmp.orgID = @orgID
	and sip.finalAction in ('A','C');

	INSERT INTO #tmpSolicitationMessages (syncSolicitationMessageID, useMessageID, title, message, finalAction)
	SELECT messageID, useMessageID, title, message, finalAction
	FROM datatransfer.dbo.sync_tr_solicitationMessages
	WHERE siteID = @siteID;

	-- remove invoice profiles
	INSERT INTO #tmpDeleteInvoiceProfiles (invoiceProfileID)
	select ip.profileID
	from dbo.tr_invoiceProfiles as ip
	left outer join dataTransfer.dbo.sync_tr_invoiceProfiles as sip on sip.orgID = @orgID and sip.profileName = ip.profileName
	where ip.orgID = @orgID
	and sip.profileID is null;

	-- invoice profiles in use
	IF EXISTS (select 1 from #tmpDeleteInvoiceProfiles) BEGIN
		IF EXISTS (select 1 from dbo.tr_GLAccounts as g inner join #tmpDeleteInvoiceProfiles as tmp on tmp.invoiceProfileID = g.invoiceProfileID)
			RAISERROR('Invoice Profile is linked to GL Accounts.',16,1);
		
		IF EXISTS (select 1 from dbo.tr_invoices as i inner join #tmpDeleteInvoiceProfiles as tmp on tmp.invoiceProfileID = i.invoiceProfileID)
			RAISERROR('Invoice Profile is linked to Invoices.',16,1);
		
		IF EXISTS (
			select c.conditionID 
			from dbo.ams_virtualGroupConditions as c 
			inner join dbo.ams_virtualGroupConditionValues as cv on c.orgID = @orgID and cv.conditionID = c.conditionID
			inner join dbo.ams_virtualGroupConditionKeys as k on k.conditionKeyID = cv.conditionKeyID and k.conditionKey = 'acctInvProf'
			inner join #tmpDeleteInvoiceProfiles as tmp on cast(tmp.invoiceProfileID as varchar(20)) = cv.conditionValue)
			RAISERROR('Invoice Profile is linked to Group Assignment Conditions.',16,1);
	END

	-- delete invoice profiles merchant profiles
	INSERT INTO #tmpDeleteInvoiceProfilesMerchantProfiles (linkID)
	select distinct ipmp.linkID
	from #tmpDeleteInvoiceProfiles as tmp
	inner join dbo.tr_invoiceProfilesMerchantProfiles as ipmp on ipmp.invoiceProfileID = tmp.invoiceProfileID
		union
	select distinct ipmp.linkID
	from dbo.tr_invoiceProfilesMerchantProfiles as ipmp
	inner join #tmpInvoiceProfilesMerchantProfiles as tmp on tmp.useInvoiceProfileID = ipmp.invoiceProfileID;

	-- delinquent days changes
	INSERT INTO #tmpInvoiceProfileUpdates (invoiceProfileID, currentNumDaysDelinquent, newNumDaysDelinquent)
	select ip.profileID, isnull(ip.numDaysDelinquent,0), isnull(tmp.numDaysDelinquent,0)
	from #tmpInvoiceProfiles as tmp
	inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileName = tmp.profileName
	where isnull(ip.numDaysDelinquent,0) <> isnull(tmp.numDaysDelinquent,0);

	BEGIN TRAN;
		-- delete invoice profiles merchant profiles
		IF EXISTS (select 1 from #tmpDeleteInvoiceProfilesMerchantProfiles)
			DELETE ipmp
			FROM dbo.tr_invoiceProfilesMerchantProfiles as ipmp
			INNER JOIN #tmpDeleteInvoiceProfilesMerchantProfiles as tmp on tmp.linkID = ipmp.linkID;

		-- delete invoice profiles
		IF EXISTS (select 1 from #tmpDeleteInvoiceProfiles)
			DELETE ip
			FROM dbo.tr_invoiceProfiles as ip
			INNER JOIN #tmpDeleteInvoiceProfiles as tmp on tmp.invoiceProfileID = ip.profileID;

		-- new solicitation messages
		IF EXISTS (select 1 from #tmpSolicitationMessages where finalAction = 'A') BEGIN
			INSERT INTO dbo.tr_solicitationMessages (siteID, title, message)
			SELECT @siteID, title, message
			FROM #tmpSolicitationMessages
			WHERE finalAction = 'A'
				EXCEPT
			SELECT siteID, title, message
			FROM dbo.tr_solicitationMessages
			WHERE siteID = @siteID;

			UPDATE tmp
			SET tmp.useMessageID = pfm.messageID
			FROM #tmpSolicitationMessages AS tmp
			INNER JOIN dbo.tr_solicitationMessages AS pfm ON pfm.siteID = @siteID
				AND pfm.message = tmp.message
			WHERE tmp.finalAction = 'A';
		END

		-- new invoice profiles
		IF EXISTS (select 1 from #tmpInvoiceProfiles where finalAction = 'A') BEGIN
			INSERT INTO dbo.tr_invoiceProfiles (orgID, profileName, status, imageExt, enableAutoPay, enforcePayOldest, allowPartialPayment, notifyEmail, numDaysDelinquent,
				enableProcessingFeeDonation, processFeeDonationDefaultSelect, solicitationMessageID, orgIdentityID)
			select @orgID, tmp.profileName, 'A', null, tmp.enableAutoPay, tmp.enforcePayOldest, tmp.allowPartialPayment, tmp.notifyEmail, nullif(tmp.numDaysDelinquent,0),
				tmp.enableProcessingFeeDonation, tmp.processFeeDonationDefaultSelect, pfm.useMessageID, tmp.orgIdentityID
			from #tmpInvoiceProfiles as tmp
			left outer join #tmpSolicitationMessages as pfm on pfm.syncSolicitationMessageID = tmp.syncSolicitationMessageID
			where tmp.finalAction = 'A'
			and not exists (select 1 from dbo.tr_invoiceProfiles where orgID = @orgID and profileName = tmp.profileName);
		END

		-- update invoice profiles
		IF EXISTS (select 1 from #tmpInvoiceProfiles where finalAction = 'C') BEGIN
			UPDATE ip
			SET ip.enableAutoPay = tmp.enableAutoPay,
				ip.enforcePayOldest = tmp.enforcePayOldest,
				ip.allowPartialPayment = tmp.allowPartialPayment,
				ip.notifyEmail = tmp.notifyEmail,
				ip.numDaysDelinquent = nullif(tmp.numDaysDelinquent,0),
				ip.enableProcessingFeeDonation = tmp.enableProcessingFeeDonation,
				ip.processFeeDonationDefaultSelect = tmp.processFeeDonationDefaultSelect,
				ip.solicitationMessageID = pfm.useMessageID,
				ip.orgIdentityID = tmp.orgIdentityID
			FROM #tmpInvoiceProfiles as tmp
			INNER JOIN dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileName = tmp.profileName
			LEFT OUTER JOIN #tmpSolicitationMessages as pfm on pfm.syncSolicitationMessageID = tmp.syncSolicitationMessageID
			WHERE tmp.finalAction = 'C';
		END

		-- new invoice profiles merchant profiles
		IF EXISTS (select 1 from #tmpInvoiceProfilesMerchantProfiles) BEGIN
			INSERT INTO dbo.tr_invoiceProfilesMerchantProfiles (invoiceProfileID, merchantProfileID)
			select ip.profileID, tmpIPMP.useMerchantProfileID
			from #tmpInvoiceProfilesMerchantProfiles as tmpIPMP
			inner join #tmpInvoiceProfiles as tmpIP on tmpIP.syncInvoiceProfileID = tmpIPMP.syncInvoiceProfileID
			inner join dbo.tr_invoiceProfiles as ip on ip.orgID = @orgID and ip.profileName = tmpIP.profileName
			where not exists (select 1 from dbo.tr_invoiceProfilesMerchantProfiles where invoiceProfileID = ip.profileID and merchantProfileID = tmpIPMP.useMerchantProfileID);
		END
	
		IF EXISTS (select 1 from #tmpInvoiceProfileUpdates) BEGIN
			-- if numDaysDelinquent is blank, ensure no invoices are delinquent - move them to closed
			INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			select i.invoiceID, getdate(), @ins_closed, @ins_delinquent, @recordedByMemberID
			from dbo.tr_invoices as i
			inner join #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
			where i.orgID = @orgID
			and i.statusID = @ins_delinquent
			and tmp.currentNumDaysDelinquent > 0
			and tmp.newNumDaysDelinquent = 0;

			IF @@ROWCOUNT > 0 BEGIN
				UPDATE i
				SET i.statusID = @ins_closed
				FROM dbo.tr_invoices as i
				INNER JOIN #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
				WHERE i.orgID = @orgID
				AND i.statusID = @ins_delinquent
				AND tmp.currentNumDaysDelinquent > 0
				AND tmp.newNumDaysDelinquent = 0;
			END

			-- else look at the invoices to see if we need to move to/from delinq
			INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			select i.invoiceID, getdate(), @ins_delinquent, @ins_closed, @recordedByMemberID
			from dbo.tr_invoices as i
			inner join #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
			where i.orgID = @orgID
			and i.statusID = @ins_closed
			and tmp.newNumDaysDelinquent > 0
			and tmp.newNumDaysDelinquent <> tmp.currentnumDaysDelinquent
			and i.dateDue <= DATEADD(DD,tmp.newNumDaysDelinquent*-1,getdate());

			IF @@ROWCOUNT > 0 BEGIN
				UPDATE i
				SET i.statusID = @ins_delinquent
				FROM dbo.tr_invoices as i
				INNER JOIN #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
				WHERE i.orgID = @orgID
				AND i.statusID = @ins_closed
				AND tmp.newNumDaysDelinquent > 0
				AND tmp.newNumDaysDelinquent <> tmp.currentnumDaysDelinquent
				AND i.dateDue <= DATEADD(DD,tmp.newNumDaysDelinquent*-1,getdate());
			END
			

			INSERT INTO dbo.tr_invoiceStatusHistory (invoiceID, updateDate, statusID, oldStatusID, enteredByMemberID)
			select i.invoiceID, getdate(), @ins_closed, @ins_delinquent, @recordedByMemberID
			from dbo.tr_invoices as i
			inner join #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
			where i.orgID = @orgID
			and i.statusID = @ins_delinquent
			and tmp.newNumDaysDelinquent > 0
			and tmp.newNumDaysDelinquent <> tmp.currentnumDaysDelinquent
			and i.dateDue > DATEADD(DD,tmp.newNumDaysDelinquent*-1,getdate());


			IF @@ROWCOUNT > 0 BEGIN
				UPDATE i
				SET i.statusID = @ins_closed
				FROM dbo.tr_invoices as i
				INNER JOIN #tmpInvoiceProfileUpdates as tmp on tmp.invoiceProfileID = i.invoiceProfileID
				WHERE i.orgID = @orgID
				AND i.statusID = @ins_delinquent
				AND tmp.newNumDaysDelinquent > 0
				AND tmp.newNumDaysDelinquent <> tmp.currentnumDaysDelinquent
				AND i.dateDue > DATEADD(DD,tmp.newNumDaysDelinquent*-1,getdate());
			END
		END
	COMMIT TRAN;

	on_done:
	IF OBJECT_ID('tempdb..#tmpInvoiceProfiles') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpInvoiceProfilesMerchantProfiles') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpSolicitationMessages') IS NOT NULL 
		DROP TABLE #tmpSolicitationMessages;
	IF OBJECT_ID('tempdb..#tmpDeleteInvoiceProfiles') IS NOT NULL 
		DROP TABLE #tmpDeleteInvoiceProfiles;
	IF OBJECT_ID('tempdb..#tmpDeleteInvoiceProfilesMerchantProfiles') IS NOT NULL 
		DROP TABLE #tmpDeleteInvoiceProfilesMerchantProfiles;
	IF OBJECT_ID('tempdb..#tmpInvoiceProfileUpdates') IS NOT NULL 
		DROP TABLE #tmpInvoiceProfileUpdates;

	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO