ALTER PROC dbo.ams_getOrgIdentityUsage
@orgID INT,
@orgIdentityID INT

AS

SET XACT_ABORT, NOCOUNT ON;
BEGIN TRY

	SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

	-- keep this in sync with ams_isOrgIdentityInUse
	DECLARE @resourceTypes TABLE (orgIdentityUsage varchar(200), usageCount int);

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'List Servers', COUNT(listID)
	FROM dbo.lists_lists
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Default for Site', COUNT(siteID)
	FROM dbo.sites
	WHERE orgID = @orgID
	AND defaultOrgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Login for Site', COUNT(siteID)
	FROM dbo.sites
	WHERE orgID = @orgID
	AND loginOrgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Publications' AS usageType, COUNT(publicationID) AS usageCount
	FROM dbo.pub_publications
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Email Blasts' AS usageType, COUNT(blastID) AS usageCount
	FROM dbo.email_emailBlasts
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Default for Org', COUNT(orgID)
	FROM dbo.organizations
	WHERE orgID = @orgID
	AND defaultOrgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'SeminarWeb' AS usageType, COUNT(participantID) AS usageCount
	FROM seminarWeb.dbo.tblParticipants
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Consent Lists' AS usageType, COUNT(consentListID) AS usageCount
	FROM platformMail.dbo.email_consentLists
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Email Messages' AS usageType, COUNT(messageID) AS usageCount
	FROM platformMail.dbo.email_messages
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'SeminarWeb Complete Seminar Reminders' AS usageType, COUNT(itemID) AS usageCount
	FROM platformQueue.dbo.queue_completeSeminarReminder
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Store' AS usageType, COUNT(storeID) AS usageCount
	FROM dbo.store
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Merchant Profile' AS usageType, COUNT(profileID) AS usageCount
	FROM dbo.mp_profiles
	WHERE orgIdentityID = @orgIdentityID;

	INSERT INTO @resourceTypes (orgIdentityUsage, usageCount)
	SELECT 'Invoice Profile' AS usageType, COUNT(profileID) AS usageCount
	FROM dbo.tr_invoiceProfiles
	WHERE orgIdentityID = @orgIdentityID;

	DELETE FROM @resourceTypes
	WHERE usageCount = 0;

	SELECT orgIdentityUsage as usageType, usageCount
	FROM @resourceTypes
	ORDER BY usageCount desc, orgIdentityUsage;

	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	RETURN 0;

END TRY
BEGIN CATCH
	IF @@trancount > 0 ROLLBACK TRANSACTION;
	SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
	EXEC dbo.up_MCErrorHandler @raise=1, @email=0;
	RETURN -1;
END CATCH
GO