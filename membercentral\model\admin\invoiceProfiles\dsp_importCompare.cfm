<cfoutput>
<div id="divImportInvoiceProfiles">
	<div class="row my-3">
		<div class="col-md-12">
			<div class="card card-box mb-1">
				<div class="card-header py-1 bg-light">
					<div class="card-header--title font-weight-bold font-size-md">
						Invoice Profiles Import Comparison
					</div>
				</div>
				<div class="card-body pb-3">
					<div class="mb-2">The following differences were found:</div>
					<cfif arrayLen(arguments.strImportResult.arrNewInvoiceProfiles)>
						<table class="table table-sm table-striped">
							<thead>
								<tr>
									<th class="font-weight-bold">Invoice Profiles to be Added</th>
								</tr>
							</thead>
							<tbody>
								<cfloop array="#arguments.strImportResult.arrNewInvoiceProfiles#" index="local.thisDiff">
									<tr>
										<td class="font-size-sm">#local.thisDiff.xmlAttributes.profilename#</td>
									</tr>
								</cfloop>
							</tbody>
						</table>
						<br/>
					</cfif>

					<cfif arrayLen(arguments.strImportResult.arrUpdateInvoiceProfiles)>
						<table class="table table-sm">
							<thead>
								<tr>
									<th class="font-weight-bold">Invoice Profiles to be Updated</th>
								</tr>
							</thead>
							<tbody>
								<cfloop query="local.qryOrgUpdateInvoiceProfiles">
									<cfquery name="local.qryThisImportInvoiceProfiles" dbtype="query">
										select syncInvoiceProfileID, profileName, enableAutoPay, enforcePayOldest, notifyEmail, allowPartialPayment,
											numDaysDelinquent, enableProcessingFeeDonation, processFeeDonationDefaultSelect, title, message, orgIdentityOrgName
										from [local].qryImportFileUpdateInvoiceProfiles
										where profileName = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.qryOrgUpdateInvoiceProfiles.profileName#">
									</cfquery>
									<cfquery name="local.qryImportFileInvProfileMerchantProfiles" dbtype="query">
										select profileName
										from [local].qryImportFileInvoiceProfilesMerchantProfiles
										where syncInvoiceProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#val(local.qryThisImportInvoiceProfiles.syncInvoiceProfileID)#">
									</cfquery>
									<cfquery name="local.qryOrgInvProfileMerchantProfiles" dbtype="query">
										select profileName
										from [local].qryOrgInvoiceProfilesMerchantProfiles
										where invoiceProfileID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryOrgUpdateInvoiceProfiles.profileID#">
									</cfquery>

									<tr>
										<td class="font-size-sm">
											<span class="font-weight-bold">#local.qryOrgUpdateInvoiceProfiles.profileName#</span>
											<cfif Compare(local.qryOrgUpdateInvoiceProfiles.enableautopay,local.qryThisImportInvoiceProfiles.enableautopay)>
												<div class="ml-2">- <span class="text-info">Enable Auto Pay</span> will change from <span class="text-danger">#YesNoFormat(local.qryOrgUpdateInvoiceProfiles.enableautopay)#</span> to <span class="text-success">#YesNoFormat(local.qryThisImportInvoiceProfiles.enableautopay)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateInvoiceProfiles.enforcePayOldest,local.qryThisImportInvoiceProfiles.enforcePayOldest)>
												<div class="ml-2">- <span class="text-info">Pay Oldest</span> will change from <span class="text-danger">#YesNoFormat(local.qryOrgUpdateInvoiceProfiles.enforcePayOldest)#</span> to <span class="text-success">#YesNoFormat(local.qryThisImportInvoiceProfiles.enforcePayOldest)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateInvoiceProfiles.allowPartialPayment,local.qryThisImportInvoiceProfiles.allowPartialPayment)>
												<div class="ml-2">- <span class="text-info">Allow Partial Payments</span> will change from <span class="text-danger">#YesNoFormat(local.qryOrgUpdateInvoiceProfiles.allowPartialPayment)#</span> to <span class="text-success">#YesNoFormat(local.qryThisImportInvoiceProfiles.allowPartialPayment)#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateInvoiceProfiles.numDaysDelinquent,local.qryThisImportInvoiceProfiles.numDaysDelinquent)>
												<div class="ml-2">- <span class="text-info">Delinquent Invoices</span> will change from <span class="text-danger"><cfif val(local.qryOrgUpdateInvoiceProfiles.numDaysDelinquent) gt 0>#local.qryOrgUpdateInvoiceProfiles.numDaysDelinquent#<cfelse>[blank]</cfif></span> to <span class="text-success"><cfif val(local.qryThisImportInvoiceProfiles.numDaysDelinquent) gt 0>#local.qryThisImportInvoiceProfiles.numDaysDelinquent#<cfelse>[blank]</cfif></span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateInvoiceProfiles.notifyEmail,local.qryThisImportInvoiceProfiles.notifyEmail)>
												<div class="ml-2">- <span class="text-info">Notify E-mails</span> will change from <span class="text-danger"><cfif len(local.qryOrgUpdateInvoiceProfiles.notifyEmail)>#local.qryOrgUpdateInvoiceProfiles.notifyEmail#<cfelse>[blank]</cfif></span> to <span class="text-success"><cfif len(local.qryThisImportInvoiceProfiles.notifyEmail)>#local.qryThisImportInvoiceProfiles.notifyEmail#<cfelse>[blank]</cfif></span></div>
											</cfif>
											<cfif local.qryImportFileInvProfileMerchantProfiles.recordCount OR local.qryOrgInvProfileMerchantProfiles.recordCount>
												<cfset local.importFileMPList = listSort(valueList(local.qryImportFileInvProfileMerchantProfiles.profileName),"textnocase")>
												<cfset local.orgMPList = listSort(valueList(local.qryOrgInvProfileMerchantProfiles.profileName),"textnocase")>
												
												<cfif Compare(local.importFileMPList,local.orgMPList)>
													<div class="ml-2">- <span class="text-info">Payment Profiles</span> will change from <span class="text-danger"><cfif len(local.orgMPList)>#local.orgMPList#<cfelse>[blank]</cfif></span> to <span class="text-success"><cfif len(local.importFileMPList)>#local.importFileMPList#<cfelse>[blank]</cfif></span></div>
												</cfif>
											</cfif>
											<cfset local.thisRowOrgEnableProcessingFeeDonation = len(local.qryOrgUpdateInvoiceProfiles.enableProcessingFeeDonation) ? #YesNoFormat(local.qryOrgUpdateInvoiceProfiles.enableProcessingFeeDonation)# : "[blank]">
											<cfset local.thisRowOrgProcessFeeDonationDefaultSelect = len(local.qryOrgUpdateInvoiceProfiles.processFeeDonationDefaultSelect) ? #YesNoFormat(local.qryOrgUpdateInvoiceProfiles.processFeeDonationDefaultSelect)# : "[blank]">
											<cfset local.thisRowOrgSolicitationTitle = len(local.qryOrgUpdateInvoiceProfiles.title) ? #local.qryOrgUpdateInvoiceProfiles.title# : "[blank]">
											<cfset local.thisRowOrgSolicitationMsg = len(local.qryOrgUpdateInvoiceProfiles.message) ? #local.qryOrgUpdateInvoiceProfiles.message# : "[blank]">
											<cfset local.thisRowImportFileEnableProcessingFeeDonation = len(local.qryThisImportInvoiceProfiles.enableProcessingFeeDonation) ? #YesNoFormat(local.qryThisImportInvoiceProfiles.enableProcessingFeeDonation)# : "[blank]">
											<cfset local.thisRowImportFileProcessFeeDonationDefaultSelect = len(local.qryThisImportInvoiceProfiles.processFeeDonationDefaultSelect) ? #YesNoFormat(local.qryThisImportInvoiceProfiles.processFeeDonationDefaultSelect)# : "[blank]">
											<cfset local.thisRowImportFileSolicitationTitle = len(local.qryThisImportInvoiceProfiles.message) ? #local.qryThisImportInvoiceProfiles.message# : "[blank]">
											<cfset local.thisRowImportFileSolicitationMsg = len(local.qryThisImportInvoiceProfiles.message) ? #local.qryThisImportInvoiceProfiles.message# : "[blank]">
											<cfif Compare(local.thisRowOrgEnableProcessingFeeDonation,local.thisRowImportFileEnableProcessingFeeDonation)>
												<div class="ml-2">- <span class="text-info">Enable Voluntary Processing Fee Donation</span> will change from <span class="text-danger">#local.thisRowOrgEnableProcessingFeeDonation#</span> to <span class="text-success">#local.thisRowImportFileEnableProcessingFeeDonation#</span></div>
											</cfif>
											<cfif Compare(local.thisRowOrgProcessFeeDonationDefaultSelect,local.thisRowImportFileProcessFeeDonationDefaultSelect)>
												<div class="ml-2">- <span class="text-info">Voluntary Processing Fee Donation Default Selection</span> will change from <span class="text-danger">#local.thisRowOrgProcessFeeDonationDefaultSelect#</span> to <span class="text-success">#local.thisRowImportFileProcessFeeDonationDefaultSelect#</span></div>
											</cfif>
											<cfif Compare(local.thisRowOrgSolicitationTitle,local.thisRowImportFileSolicitationTitle)>
												<div class="ml-2">- <span class="text-info">Solicitation Title</span> will change from <span class="text-danger">#local.thisRowOrgSolicitationTitle#</span> to <span class="text-success">#local.thisRowImportFileSolicitationTitle#</span></div>
											</cfif>
											<cfif Compare(local.thisRowOrgSolicitationMsg,local.thisRowImportFileSolicitationMsg)>
												<div class="ml-2">- <span class="text-info">Solicitation Message</span> will change from <span class="text-danger">#local.thisRowOrgSolicitationMsg#</span> to <span class="text-success">#local.thisRowImportFileSolicitationMsg#</span></div>
											</cfif>
											<cfif Compare(local.qryOrgUpdateInvoiceProfiles.orgIdentityOrgName,local.qryThisImportInvoiceProfiles.orgIdentityOrgName)>
												<div class="ml-2">- <span class="text-info">Organization Identity</span> will change from <span class="text-danger">#local.qryOrgUpdateInvoiceProfiles.orgIdentityOrgName#</span> to <span class="text-success">#local.qryThisImportInvoiceProfiles.orgIdentityOrgName#</span></div>
											</cfif>
										</td>
									</tr>
								</cfloop>
							</tbody>
						</table>
						<br/>
					</cfif>

					<cfif arrayLen(arguments.strImportResult.arrRemoveInvoiceProfiles)>
						<table class="table table-sm table-striped">
							<thead>
								<tr>
									<th class="font-weight-bold">Invoice Profiles to be Removed</th>
								</tr>
							</thead>
							<tbody>
								<cfloop array="#arguments.strImportResult.arrRemoveInvoiceProfiles#" index="local.thisDiff">
									<tr>
										<td class="font-size-sm">#local.thisDiff.xmlAttributes.profileName#</td>
									</tr>
								</cfloop>
							</tbody>
						</table>
						<br/>
					</cfif>

					<cfif arrayLen(arguments.strImportResult.arrNewSolicitationmsgs)>
						<table class="table table-sm table-striped">
							<thead>
								<tr>
									<th class="font-weight-bold">Solicitation Messages to be Added</th>
								</tr>
							</thead>
							<tbody>
								<cfloop array="#arguments.strImportResult.arrNewSolicitationmsgs#" index="local.thisDiff">
									<tr>
										<td class="font-size-sm">Title: #local.thisDiff.xmlAttributes.title#<br/>Message: #local.thisDiff.xmlAttributes.message#</td>
									</tr>
								</cfloop>
							</tbody>
						</table>
						<br/>
					</cfif>
					
					<br/>
					<form name="frmImportInvoiceProfiles" id="frmImportInvoiceProfiles">
						<input type="hidden" name="impThreadID" id="impThreadID" value="#arguments.threadID#">
						<button type="button" class="btn btn-sm btn-primary" name="btnImportChanges" id="btnImportChanges" onclick="continueInvoiceProfilesImport();">Continue Import</button> &nbsp; 
						<button type="button" class="btn btn-sm btn-secondary" name="btnReturnToQuestions" id="btnReturnToQuestions" onclick="gotoInvoiceProfiles();">Cancel Import</button>
					</form>
				</div>
			</div>
		</div>
	</div>
</div>

<div id="divImportInvoiceProfilesSubmitArea" style="display:none;"></div>

<div id="divImportInvoiceProfilesLoading" style="display:none;">
	<h4>Invoice Profiles Import</h4>
	<div class="mt-4 text-center">
		<div class="spinner-border" role="status"></div>
		<div class="mt-2 font-weight-bold">Please wait while we import the changes.</div>
	</div>
</div>
</cfoutput>