<cfcomponent extends="model.admin.admin" output="false">
	<cfset variables.defaultEvent = 'controller'>
	
	<cffunction name="controller" access="public" output="false" returntype="string" hint="controller for this app">
		<cfargument name="Event" type="any">
		<cfscript>
			var local = structNew();
			// RUN ASSIGNED METHOD --------------------------------------------------------------------- ::
			local.methodToRun = this[arguments.event.getValue('meth')];

			// PASS THE ARGUMENT COLLECTION TO THE CURRENT METHOD AND EXECUTE IT. ----------------------- ::
			return local.methodToRun(arguments.event);
		</cfscript>
	</cffunction>

	<cffunction name="getInvoiceContentList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'desc');
			arguments.event.setValue('posStart',int(val(arguments.event.getValue('start',0))));
			arguments.event.setValue('count',int(val(arguments.event.getValue('length',10))));
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfquery name="local.qryContent" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @orgID int, @totalCount int, @posStart int, @posStartAndCount int, @ApplicationCreatedContentSRTID int, @GLAccountsAdminSRTID int;
			DECLARE @tblGLAA TABLE (siteID int, siteResourceID int PRIMARY KEY);
			SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('posStart')#">;
			SET @posStartAndCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('count')#">;
			SET @orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgID')#">;
			SELECT @ApplicationCreatedContentSRTID = resourceTypeID FROM dbo.cms_siteResourceTypes WHERE resourceType = 'ApplicationCreatedContent';
			SELECT @GLAccountsAdminSRTID = resourceTypeID FROM dbo.cms_siteResourceTypes WHERE resourceType = 'GLAccountsAdmin';

			IF OBJECT_ID('tempdb..##tmpContent') IS NOT NULL
				DROP TABLE ##tmpContent;
			CREATE TABLE ##tmpContent (contentid int PRIMARY KEY, contenttitle varchar(1000), isInUseIT bit, isInUseGL bit, row int);

			INSERT INTO @tblGLAA (siteID, siteResourceID)
			SELECT s.siteID, sr.siteResourceID
			FROM dbo.sites as s
			INNER JOIN dbo.cms_siteResources as sr on sr.siteID = s.siteID
				and sr.siteResourceStatusID = 1
				and sr.resourceTypeID = @GLAccountsAdminSRTID
			WHERE s.orgID = @orgID;

			INSERT INTO ##tmpContent (contentid, contenttitle, isInUseIT, isInUseGL, row)
			SELECT c.contentID, glContent.contentTitle, 0, 0, ROW_NUMBER() OVER (ORDER BY glContent.contentTitle #arguments.event.getValue('orderDir')#) AS row
			FROM @tblGLAA as sr
			INNER JOIN dbo.cms_siteResources as sr2 on sr2.siteID = sr.siteID
				and sr2.siteResourceStatusID = 1
				and sr2.resourceTypeID = @ApplicationCreatedContentSRTID
				and sr2.parentSiteResourceID = sr.siteResourceID
			INNER JOIN dbo.cms_content as c on c.siteID = sr2.siteID
				and c.siteResourceID = sr2.siteResourceID
			CROSS APPLY dbo.fn_getContent(c.contentID,1) as glContent;
				SET @totalCount = @@ROWCOUNT;

			UPDATE tmp
			SET tmp.isInUseIT = case when ( 
				select count(*)
				from dbo.tr_invoiceTransactions as it
				inner join dbo.cms_contentVersions as cv on cv.contentLanguageID = it.messageContentVersionID
				inner join dbo.cms_contentLanguages as cl2 on cl2.contentLanguageID = cv.contentLanguageID and cl2.contentID = tmp.contentid
				where it.orgID = @orgID
				) > 0 then 1 else 0 end
			FROM ##tmpContent as tmp;

			UPDATE tmp
			SET tmp.isInUseGL = case when ( 
				select count(*)
				from dbo.tr_GLAccounts as gl
				where gl.orgID = @orgID
				and gl.invoiceContentID = tmp.contentid
				and gl.status <> 'D'
				) > 0 then 1 else 0 end
			FROM ##tmpContent as tmp;

			select contentid, contenttitle, isInUseIT, isInUseGL, @totalCount as totalCount
			from ##tmpContent
			where row > @posStart AND row <= @posStartAndCount
			ORDER BY row;

			IF OBJECT_ID('tempdb..##tmpContent') IS NOT NULL 
				DROP TABLE ##tmpContent;
			
			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfset local.arrData = []>
		<cfloop query="local.qryContent">
			<cfset local.arrData.append({
				"contentid": local.qryContent.contentid,
				"contenttitle": local.qryContent.contenttitle,
				"candelete": not local.qryContent.isInUseIT and not local.qryContent.isInUseGL,
				"DT_RowId": "contentRow_#local.qryContent.contentid#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": val(local.qryContent.totalCount),
			"recordsFiltered": val(local.qryContent.totalCount),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>

	<cffunction name="getAccountList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset local.rootName = arguments.event.getValue('mc_siteInfo.ORGSHORTNAME') & ' Chart of Accounts'>

		<cfquery name="local.qryGLAccounts" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			WITH allGLs as (
				select GLAccountID, parentGLAccountID, AccountTypeID, AccountType, accountName, accountCode, InvoiceProfileID, GLCode, thePath, status, isSystemAccount, 
					acctSysName, acctSysClass, ROW_NUMBER() OVER (ORDER BY thePath ASC) AS row
				from dbo.fn_getRecursiveGLAccountsWithAccountTypes(<cfqueryparam value="#arguments.event.getValue('mc_siteinfo.orgid')#" cfsqltype="CF_SQL_INTEGER">)
			), filteredGLs as (
				select GLAccountID, parentGLAccountID, thePath
				from allGLs
				<cfif len(arguments.event.getValue('kw',''))>
					where accountName+' '+isnull(accountCode,'') like '%' + <cfqueryparam value="#arguments.event.getValue('kw','')#" cfsqltype="CF_SQL_VARCHAR"> + '%'
						union all
					select allGLs.GLAccountID, allGLs.parentGLAccountID, allGLs.thePath
					from allGLs
					inner join filteredGLs on left(filteredGLs.thePath,len(allGLs.thePath)+1) = allGLs.thePath + '.'
				</cfif>
			)
			select tmp.GLAccountID, glt.allowAddAccounts, tmp.AccountTypeID, tmp.AccountType, tmp.accountName, tmp.accountCode, tmp.profileName, tmp.thePath, 
				tmp.status, tmp.isSystemAccount, tmp.acctSysName, tmp.acctSysClass, 
				case when tmp.isSystemAccount = 1 and tmp.glCode in ('DEFERREDREVENUE','DEFERREDTAX','SALESTAX') and tmp.status = 'A' then 0 else 1 end as canEdit,
				case when tmp.isSystemAccount = 0 and tmp.status = 'A' then 1 else 0 end as canDelete,
				case when tmp.isSystemAccount = 0 and tmp.status = 'I' then 1 else 0 end as canUnDelete,
				tmp.parentGLAccountID, tmp.row
			from (	
				select distinct allGLs.GLAccountID, allGLs.parentGLAccountID, allGLs.AccountTypeID, allGLs.AccountType, allGLs.accountName, 
					allGLs.accountCode, ip.profileName, allGLs.GLCode, allGLs.thePath, allGLs.status, allGLs.isSystemAccount, 
					allGLs.acctSysName, allGLs.acctSysClass, allGLs.row
				from filteredGLs 
				inner join allGLs on allGLs.thePath = filteredGLs.thePath
				left join dbo.tr_invoiceProfiles as ip on ip.profileID = allGLs.InvoiceProfileID
					and ip.orgID = <cfqueryparam cfsqltype="cf_sql_integer" value="#arguments.event.getValue('mc_siteinfo.orgid')#">
					and ip.status <> 'D'
					and allGLs.AccountType = 'Revenue'
			) as tmp
			inner join dbo.tr_glaccountTypes as glt on glt.accountTypeID = tmp.accountTypeID			
			order by tmp.row
		</cfquery>

		<cfset local.arrGLAccounts = []>
		<cfset local.parentIDX = 0>
		<cfloop query="local.qryGLAccounts">
			<cfif local.qryGLAccounts.GLAccountID gt 0>
				<cfset local.rowID = "gla#val(local.qryGLAccounts.GLAccountID)#">
				<cfif val(local.qryGLAccounts.parentGLAccountID)>
					<cfset local.classList = "child-of-gla#val(local.qryGLAccounts.parentGLAccountID)#">
				<cfelse>
					<cfset local.classList = "child-of-type#local.qryGLAccounts.AccountTypeID#">
				</cfif>

				<cfquery name="local.qryChildren" dbtype="query" maxrows="1">
					SELECT GLAccountID
					FROM [local].qryGLAccounts
					WHERE parentGLAccountID = #val(local.qryGLAccounts.GLAccountID)#
				</cfquery>
			<cfelse>
				<cfset local.rowID = "type#local.qryGLAccounts.AccountTypeID#">
				<cfset local.classList = "child-of-gridRoot">

				<cfquery name="local.qryChildren" dbtype="query" maxrows="1">
					SELECT GLAccountID
					FROM [local].qryGLAccounts
					WHERE GLAccountID > 0
					AND accountTypeID = #val(local.qryGLAccounts.AccountTypeID)#
				</cfquery>
			</cfif>

			<cfset local.arrGLAccounts.append({
				"level": ListLen(local.qryGLAccounts.thePath,"."),
				"GLAccountID": local.qryGLAccounts.GLAccountID,
				"accountTypeID": local.qryGLAccounts.AccountTypeID,
				"accountType": local.qryGLAccounts.AccountType,
				"accountName": local.qryGLAccounts.accountName,
				"accountCode": local.qryGLAccounts.accountCode,
				"isSystemAccount": local.qryGLAccounts.isSystemAccount,
				"profileName": local.qryGLAccounts.profileName,
				"status": local.qryGLAccounts.status,
				"acctSysName": local.qryGLAccounts.acctSysName,
				"acctSysClass": local.qryGLAccounts.acctSysClass,
				"allowAddAccounts": val(local.qryGLAccounts.allowAddAccounts),
				"canEdit": local.qryGLAccounts.canEdit,
				"canDelete": local.qryGLAccounts.canDelete,
				"canUnDelete": local.qryGLAccounts.canUnDelete,
				"hasChildren": local.qryChildren.recordCount gt 0,
				"DT_RowId": local.rowID,
				"DT_RowClass": local.classList
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrGLAccounts),
			"recordsFiltered": arrayLen(local.arrGLAccounts),
			"data": local.arrGLAccounts
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getAccountListSelector" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>
		<cfset arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))))>

		<cfquery name="local.qryGLAccounts" datasource="#application.dsn.memberCentral.dsn#">
			SET NOCOUNT ON;

			DECLARE @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;

			WITH allGLs as (
				select GLAccountID, isnull(parentGLAccountID,0) as parentGLAccountID, accountName, accountCode, thePath, status,
					ROW_NUMBER() OVER (ORDER BY thePath ASC) AS row
				from dbo.fn_getRecursiveGLAccounts(@orgID)
				where accountTypeID = <cfqueryparam value="#arguments.event.getValue('glatid',0)#" cfsqltype="CF_SQL_INTEGER">
				AND status IN ('A')
			), filteredGLs as (
				select GLAccountID, parentGLAccountID, thePath
				from allGLs
				<cfif len(arguments.event.getValue('kw',''))>
					where accountName+' '+isnull(accountCode,'') like '%' + <cfqueryparam value="#arguments.event.getValue('kw','')#" cfsqltype="CF_SQL_VARCHAR"> + '%'
						union all
					select allGLs.GLAccountID, allGLs.parentGLAccountID, allGLs.thePath
					from allGLs
					inner join filteredGLs on left(filteredGLs.thePath,len(allGLs.thePath)+1) = allGLs.thePath + '.'
				</cfif>
			)
			select distinct allGLs.GLAccountID, allGLs.parentGLAccountID, allGLs.accountName, allGLs.accountCode,
				allGLs.thePath, allGLs.status, allGLs.row
			from filteredGLs 
			inner join allGLs on allGLs.thePath = filteredGLs.thePath
			order by allGLs.row
		</cfquery>

		<cfset local.arrGLAccounts = []>
		<cfloop query="local.qryGLAccounts">
			<cfset local.rowID = "gla#val(local.qryGLAccounts.GLAccountID)#">
			<cfset local.classList = val(local.qryGLAccounts.parentGLAccountID) ? "child-of-gla#val(local.qryGLAccounts.parentGLAccountID)#" : "child-of-gridRoot">

			<cfquery name="local.qryChildren" dbtype="query" maxrows="1">
				SELECT GLAccountID
				FROM [local].qryGLAccounts
				WHERE parentGLAccountID = #val(local.qryGLAccounts.GLAccountID)#
			</cfquery>

			<cfset local.arrGLAccounts.append({
				"level": ListLen(local.qryGLAccounts.thePath,"."),
				"GLAccountID": local.qryGLAccounts.GLAccountID,
				"accountName": local.qryGLAccounts.accountName,
				"accountCode": local.qryGLAccounts.accountCode,
				"status": local.qryGLAccounts.status,
				"hasChildren": local.qryChildren.recordCount gt 0,
				"DT_RowId": local.rowID,
				"DT_RowClass": local.classList
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": arrayLen(local.arrGLAccounts),
			"recordsFiltered": arrayLen(local.arrGLAccounts),
			"data": local.arrGLAccounts
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getLimitScheduleList" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('orderDir',form['order[0][dir]'] ?: 'asc');
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;

			declare @orgID int = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;

			select ls.scheduleID, ls.limitName, ls.startdate, ls.enddate, glDetail.thePathExpanded
			from dbo.tr_limitSchedules as ls
			inner join dbo.fn_getRecursiveGLAccounts(@orgID) as glDetail on glDetail.GLAccountID = ls.overflowGLAccountID
			where ls.orgID = @orgID
			and ls.isActive = 1
			order by ls.limitName #arguments.event.getValue('orderDir')#;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryData">
			<cfset arrayAppend(local.data, {
				"scheduleID": local.qryData.scheduleID,
				"limitName": local.qryData.limitName,
				"thePathExpanded": local.qryData.thePathExpanded,
				"startDate": dateFormat(local.qryData.startDate,"m/d/yyyy"),
				"endDate": dateFormat(local.qryData.endDate,"m/d/yyyy"),
				"DT_RowId": "row_#local.qryData.scheduleID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryData.recordCount,
			"recordsFiltered": local.qryData.recordCount,
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getLimitGLAccount" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfscript>
			var local = structNew();
			arguments.event.setValue('draw',int(val(arguments.event.getValue('draw',1))));
		</cfscript>

		<cfquery datasource="#application.dsn.membercentral.dsn#" name="local.qryData">
			SET NOCOUNT ON;

			declare @orgID int, @scheduleID int;
			set @orgID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('mc_siteinfo.orgid')#">;
			set @scheduleID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.event.getValue('scheduleID',0)#">;

			select lt.limitID, lt.scheduleID, ls.limitName, lt.maxAmount, lt.glOrder, glDetail.thePathExpanded
			from dbo.tr_limits as lt
			inner join dbo.tr_limitSchedules as ls on ls.scheduleID = lt.scheduleID
			inner join dbo.fn_getRecursiveGLAccounts(@orgID) as glDetail on glDetail.GLAccountID = lt.glaccountID 
			where ls.orgID = @orgID
			and ls.scheduleID = @scheduleID
			and ls.isActive = 1
			order by lt.glOrder;
		</cfquery>

		<cfset local.data = []>
		<cfloop query="local.qryData">
			<cfset arrayAppend(local.data, {
				"limitID": local.qryData.limitID,
				"scheduleID": local.qryData.scheduleID,
				"maxAmount": DollarFormat(local.qryData.maxAmount),
				"thePathExpanded": local.qryData.thePathExpanded,
				"canmoveup": local.qryData.currentRow GT 1,
				"canmovedown": local.qryData.currentRow NEQ local.qryData.recordCount,
				"DT_RowId": "row_#local.qryData.limitID#"
			})>
		</cfloop>

		<cfset local.returnStruct = {
			"success": true,
			"draw": arguments.event.getValue('draw'),
			"recordsTotal": local.qryData.recordCount,
			"recordsFiltered": local.qryData.recordCount,
			"data": local.data
		}>

		<cfreturn serializeJSON(local.returnStruct)>
	</cffunction>

	<cffunction name="getGLAccountsAuditLog" access="public" output="false" returntype="string">
		<cfargument name="Event" type="any">

		<cfset var local = structNew()>

		<cfset local.arrAudit = CreateObject('component', 'model.system.platform.auditLog').getGLAccountsAuditLog(
			orgID=arguments.event.getValue('mc_siteinfo.orgID'),
			keywords=arguments.event.getValue('fDescription',''),
			dateFrom=arguments.event.getValue('fDateFrom',''),
			dateTo=arguments.event.getValue('fDateTo',''),
			limit=50
		).arrValue>

		<cfset local.memberIDList = "">
		<cfset local.arrData = []>
		<cfloop array="#local.arrAudit#" item="local.thisAuditEntry" index="local.index">
			<cfif NOT listFind(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
				<cfset local.memberIDList = listAppend(local.memberIDList, local.thisAuditEntry["ACTORMEMBERID"])>
			</cfif>
			<cfset local.actionDate = parseDateTime(local.thisAuditEntry["ACTIONDATE"])>

			<cfset local.arrData.append({
				"date": "#DateFormat(local.actionDate, "m/d/yy")# #TimeFormat(local.actionDate, "h:mm tt")#",
				"memberid": local.thisAuditEntry["ACTORMEMBERID"],
				"description": replace(local.thisAuditEntry["MESSAGE"],"#chr(13)##chr(10)#","<br/>","ALL"),
				"DT_RowId": "auditlogrow_#local.index#"
			})>
		</cfloop>

		<cfif arrayLen(local.arrData)>
			<cfquery name="local.qryMembers" datasource="#application.dsn.membercentral.dsn#">
				SET NOCOUNT ON;
				SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

				SELECT m.memberID, m2.firstName + ' ' + m2.lastName AS membername
				FROM dbo.ams_members AS m
				INNER JOIN dbo.ams_members AS m2 ON m2.memberID = m.activeMemberID
				WHERE m.memberID IN (<cfqueryparam cfsqltype="cf_sql_integer" value="#local.memberIDList#" list="true">);

				SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
			</cfquery>

			<cfset var strMemberName = structNew()>
			<cfloop query="local.qryMembers">
				<cfset strMemberName["#local.qryMembers.memberID#"] = local.qryMembers.memberName>
			</cfloop>

			<cfset local.arrData = arrayMap(local.arrData, (thisData) => { 
				thisData["actor"] = structKeyExists(strMemberName, thisData.memberid) ? strMemberName["#thisData.memberid#"] : "";
				return thisData;
			})>
		</cfif>

		<cfset local.returnStruct = {
			"success": true,
			"draw": int(val(arguments.event.getValue('draw',1))),
			"recordsTotal":  arrayLen(local.arrData),
			"recordsFiltered":  arrayLen(local.arrData),
			"data": local.arrData
		}>

		<cfreturn serializejson(local.returnStruct)>
	</cffunction>
</cfcomponent>