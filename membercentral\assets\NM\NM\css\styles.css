/* CSS RESET ELEMENTS --------------------------------------------------------------------------------------*/
body,img,p,h1,h2,h3,h4,h5,h6{ border:none; list-style:none; margin:0; padding:0; }
body{ 
  font-family:Arial,helvetica,sans-serif;
  margin:0 0 0 0; 
  padding:0 0 0 0; 
  background:#c4c7ff;
}
/*----------------------------------------------------------------------------------------------------------*/
#header{ overflow:none; padding:0px; position:relative; background:#0b0a43; border-bottom:2px solid white;}
#headerContainer{ padding-top:20px; margin:auto auto; overflow:none; position:relative; background:url(/images/mastheadBG.jpg) no-repeat; height:360px; max-width:1400px; }
#headerContainerInner { width:940px; margin:auto auto; padding:10px; }
#masthead { background:#fff; width:960px; margin:auto auto; margin-top:45px; padding-top:18px; text-align:center; }
/*----------------------------------------------------------------------------------------------------------*/
#searchArea{ float:right; margin-right:5px; margin-top:4px; text-align:right; width:30%; }
input.searchField{ background:url(/images/searchIco.png) no-repeat; background-color:#fff; border:1px solid #adb4bb; color:#163458; font-family:Arial; font-size:13px;  line-height:120%; margin-top:0; padding:5px; padding-left:24px; width:162px; }
button.searchButton{ vertical-align:middle; border:1px solid #adb4bb; border-left:0px; font-size:13px; font-family:Arial; color:#163458; padding:5px 5px 5px 5px; background:#d3e8f0; margin:0; margin-left:-5px; vertical-align:top; }
/*----------------------------------------------------------------------------------------------------------*/
#wrapper{ margin:auto auto; width:960px; position:relative;  }
/*----------------------------------------------------------------------------------------------------------*/
.leftInnerShadow { background-image:url(/assets/admin/images/lay_shadowInnerLeft.png); background-repeat:repeat-y; }
.rightInnerShadow { background-image:url(/assets/admin/images/lay_shadowInnerRight.png); background-repeat:repeat-y; }
/*----------------------------------------------------------------------------------------------------------*/
#mainNav { background-color:#fff; padding-top:4px; overflow:none; position:relative;  }
#navigation { background-color:#fff; height:60px; overflow:none; position:relative; }
#navigation ul { list-style:none; color:#ccc; margin:auto auto; padding:0;}
#navigation ul li { float:left; padding:10px 10px 0px; }
#navigation ul a { 
	color:#163458; 
	display:block; 
	font-size:15px; 
	padding:15px 20px 15px 20px; 
	text-decoration:none; 
	border-radius:5px 5px; 
	-moz-border-radius:5px 5px; 
	-webkit-border-radius:5px 5px; 
	
	/*border-bottom-right-radius:0px 0px; 
	border-bottom-right-radius:0px 0px; 
	-moz-border-bottom-right-radius:0px 0px; 
	-webkit-border-bottom-right-radius:0px 0px;
	
	border-bottom-left-radius:0px 0px; 
	border-bottom-left-radius:0px 0px; 
	-moz-border-bottom-left-radius:0px 0px; 
	-webkit-border-bottom-left-radius:0px 0px;
	*/
	}
#navigation ul a:hover { color:#fff; background: #163458; }
#navigation ul a.activeSection { color:#fff; background: #163458; }
/*----------------------------------------------------------------------------------------------------------*/
#content{ background-color:white;  }
#slider img.banner { max-width:960px!important; max-height:290px!important; }
#slider ul, li {  margin:0; padding:0; }
/*HOMEPAGE: ------------------------------------------------------------------------------------------------*/
#events { float:left; padding:30px 20px 30px 35px; max-width:360px; }
#events .bigDateNum { color:#163458; font-family:Arial; font-size:60px; text-align:center; margin:0; padding:0; }
#events .bigDateTxt { color:#163458; font-family:Arial; font-size:12px; text-align:center; margin-top:-10px; padding:0; }
#eventsDates { float:left; padding:15px; }
#eventsText { float:right; max-width:210px;  }
#resources { float:right; padding:30px 20px 30px 20px; max-width:500px; }
/*INTERIOR: ------------------------------------------------------------------------------------------------*/
#mainContent{ padding:10px; float:right; max-width:700px; min-width:680px; vertical-align:top; }
#mainContent p { padding-bottom:15px; }
.bodyEditor p { padding-bottom:15px; }
#interiorLeftCol { width:240px; float:left; vertical-align:top; }
#leftNav { padding:10px 0px 20px; margin:20px 5px 5px 35px; color:#c4c7ff; background:#163458; border:#9799c9 solid 4px; border-radius:15px 15px; -moz-border-radius:15px 15px; -webkit-border-radius:15px 15px; }
#leftNav a { color:#c4c7ff; text-decoration:none; font-size:14px; letter-spacing:1px; font-weight:100; }
#leftNav a:hover { text-decoration:underline; }
#leftNav li { padding-bottom:20px; }
#leftNav ul { margin:0px; }
#leftLower { padding:20px 10px; margin:28px 5px 5px 35px; border:#cccccc solid 4px; border-radius:15px 15px; -moz-border-radius:15px 15px; -webkit-border-radius:15px 15px;}
/*----------------------------------------------------------------------------------------------------------*/
#aboutNMTLA { padding:40px; padding-top:6px; }
#aboutNMTLA img { padding:0px 20px 3px 0px; }
/*FOOTER: --------------------------------------------------------------------------------------------------*/
#footer { background-color:#c4c7ff; color:#fff; text-align:center; padding:10px 0 10px 0; position:relative;  }
#footer ul{ list-style:none; color:#ccc; margin:auto auto; padding:0;}
#footer ul li{ float:left; padding:5px 15px; }
#footer ul a{ color:#163458; display:block; font-size:14px; padding:12px 20px 12px 20px; text-decoration:none; border-radius:5px 5px; -moz-border-radius:5px 5px; -webkit-border-radius:5px 5px; }
#footer ul a:hover { color:#fff; background: url(/images/navHoverBG.png) no-repeat #163458; }
/*----------------------------------------------------------------------------------------------------------*/
.clear{ clear:both; }
/*----------------------------------------------------------------------------------------------------------*/
#loginBox{ border:1px solid #333; margin:auto auto; width:60%; margin-top:10px; margin-bottom:10px; }
#loginHeader{ background-color:#355b8c; color:#fff; font-size:1.1em; font-weight:bold; padding:5px; }
/*----------------------------------------------------------------------------------------------------------*/
/* TOOLBAR STYLES ------------------------------------------------------------------------------------------*/
#toolBarArea{ margin:15px 0 15px 0; float:left;width:100%;}
#toolBar{  background:#fff; border-top:1px solid #000000; border-bottom:1px solid #000000; color:#355b8c; padding:5px 0 5px 0; }
#toolBar a{ color:#b4191a; font-size:1em; }
/*----------------------------------------------------------------------------------------------------------*/
.topLinksMenu {display: flex;align-items: center;gap: 30px;}
.topLinksMenu span {display: inline-block;}
.topLinksMenu .topLink {color: #fff;text-decoration: none;font-family: Arial;font-size: 13px;display: inline-flex;align-items: center;gap: 5px;}
.topLinksMenu .topLink:hover {color: #d3e8f0;text-decoration: none;}
.topLinksMenu .topLink i {font-size: 14px;}
