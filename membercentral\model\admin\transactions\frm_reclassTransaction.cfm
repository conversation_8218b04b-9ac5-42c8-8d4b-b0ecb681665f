<cfsavecontent variable="local.reclassTransJS">
	<cfoutput>
	#local.strGLAcctWidget.js#
	<script type="text/javascript">
		var reclassRow = 0;

		function disableSaveBtn() { top.$('##btnMCModalSave').prop('disabled',true); }
		function enableSaveBtn() { top.$('##btnMCModalSave').prop('disabled',false); }

		function checkReclassAmt() {
			$('input.reclassAmt').each(function(index) { $(this).val(formatCurrency($(this).val())); });
		}
		function addReclassRow() {
			checkReclassAmt();
			var rowCount = Number($('##currrowid').val());
			var nextRow = rowCount + 1;

			var reclassRowTemplateSource = $('##mc_reclassRow').html();
			var reclassRowTemplate = Handlebars.compile(reclassRowTemplateSource);
			$('tbody##tblDefReclassAmt').append(reclassRowTemplate({nextRow: nextRow}));
			cloneGLWidgetForRow(nextRow);
			$('##currrowid').val(nextRow);
		}

		function cloneGLWidgetForRow(rowNumber) {
			var originalWidget = $('tbody##tblDefReclassAmt tr:first-child td:nth-child(3)').html();

			var tempContainer = $('<div>').html(originalWidget);

			tempContainer.find('input[type="hidden"]').attr({
				'name': 'reclassAmt_' + rowNumber + '_gl',
				'id': 'reclassAmt_' + rowNumber + '_gl'
			}).addClass('reclassAmtGL').val('');

			tempContainer.find('input[type="text"]').attr({
				'id': 'reclassAmt_' + rowNumber + '_glPath'
			}).val('(no account selected)');

			tempContainer.find('button.mcgl_selectbtn').attr({
				'name': 'btnSelectGLAccount_reclassAmt_' + rowNumber + '_gl',
				'data-glinpid': 'reclassAmt_' + rowNumber + '_gl',
				'data-glinppath': 'reclassAmt_' + rowNumber + '_glPath'
			});
			
			tempContainer.find('label').attr({
				'for': 'reclassAmt_' + rowNumber + '_glPath'
			});

			var newFieldName = 'reclassAmt_' + rowNumber + '_gl';
			if (typeof window['mcgl_' + newFieldName + '_selURL'] === 'undefined') {
				window['mcgl_' + newFieldName + '_selURL'] = window['mcgl_reclassAmt_1_gl_selURL'];
			}

			$('##glWidget_' + rowNumber).html(tempContainer.html());
		}
		function removeReclassRow(i) {
			$('tr##reclassRow_'+i).remove();
		}
		function validateReclassForm() {
			disableSaveBtn();
			mca_hideAlert('err_reclass_trans');

			var reclassAmt = parseFloat('#local.reclassAmount#');
			var totalAmt = 0;
			var missingSplitAmt = 0;
			var glselect = 1;
			var glchange = 0;
			var arrReq = [];

			$('tbody##tblDefReclassAmt input.reclassAmt').each(function(index) { 
				var thisReclassAmt = Number($(this).val().replace(/[^0-9\.]+/g,"")); 
				totalAmt += parseFloat(thisReclassAmt); 
				if (parseFloat(thisReclassAmt) == 0) missingSplitAmt = 1;
			});
			
			if($('##reclassSplitDate').val() == '') arrReq[arrReq.length] = 'Enter the Reclass/Split Date.';
			if($('##invoiceDueDate').val() == '') arrReq[arrReq.length] = 'Enter the Invoice Due Date.';
			if(formatCurrency(reclassAmt) != formatCurrency(totalAmt)) arrReq[arrReq.length] = 'The sum of split amounts must be equal to $' + formatCurrency(reclassAmt) + '.';
			$('tbody##tblDefReclassAmt input.reclassAmtGL').each(function(index) { 
				if($(this).val() == '') {
					glselect = 0;
					return false;
				} else if (glchange == 0 && $(this).val() != #local.qryTransaction.creditGLAccountID#) {
					glchange = 1;
				}
			});
			if (missingSplitAmt == 1) arrReq[arrReq.length] = 'Each split must be greater than $0.00.';
			if (glselect == 0) arrReq[arrReq.length] = 'Select a GL Account for all splits.';
			else if ($('input.reclassAmt').length == 1 && glchange == 0) arrReq[arrReq.length] = 'Cannot reclass revenue to the same revenue GL.';

			if(arrReq.length) {
				mca_showAlert('err_reclass_trans', arrReq.join('<br/>'));
				enableSaveBtn();
				return false;
			}

			$('##divReclassForm').addClass('d-none');
			$('##divReclassFormloadingDIV').removeClass('d-none');
			top.$('##btnMCModalSave').remove();
			return true;
		}

		$(function() {
			mca_setupDatePickerField('reclassSplitDate', '#DateFormat(local.earliestDate,'m/d/yyyy')#', '#DateFormat(now(),'m/d/yyyy')#');
			mca_setupDatePickerField('invoiceDueDate');
			mca_setupCalendarIcons('frmReclass');
			$('##reclassAmt_1_gl').addClass('reclassAmtGL');
			$(document).on('blur','input.reclassAmt', function() {
				checkReclassAmt();
			});

			<cfif NOT local.DisallowSplitEarliestDate>
				top.MCModalUtils.buildFooter({
					classlist: 'd-flex justify-content-between',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmReclass :submit").click',
					extrabuttonlabel: 'Save'
				});
			<cfelse>
				top.MCModalUtils.buildFooter({});
			</cfif>
		});
	</script>

	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.reclassTransJS)#">

<cfoutput>
<div id="divReclassTrans" class="p-3">
	<form name="frmReclass" id="frmReclass" action="#local.saveReclassLink#" method="post" onSubmit="return validateReclassForm();">
	<input type="hidden" name="tid" id="tid" value="#local.qryTransaction.transactionid#">
	<input type="hidden" name="currrowid" id="currrowid" value="1">

	<cfif NOT local.DisallowSplitEarliestDate>
		<!--- hidden submit triggered from parent --->
		<button type="submit" class="d-none"></button>
	</cfif>

	<div class="mb-4">
		<div class="mt-2">
			<b>Sale to Reclass/Split:</b><br/>
			#dateformat(local.qryTransaction.transactionDate,"m/d/yyyy")# &nbsp; #local.qryTransaction.detail#<br/>
		</div>
		<div class="mt-2">
			<b>Assignee:</b><br/>
			#local.qryAssignee.assigneeName#<br/>
			<cfif len(local.qryAssignee.assigneeCompany)>#local.qryAssignee.assigneeCompany#<br/></cfif>
		</div>
	</div>

	<div id="err_reclass_trans" class="alert alert-danger mb-2 d-none"></div>

	<div id="divReclassForm">
		<div class="mb-3">
			This sale's current revenue is <strong>#dollarFormat(local.currentSaleAmount)#</strong>.
			<cfif local.reclassAmount is not local.currentSaleAmount>
				<br/>You may reclass/split <strong>#dollarFormat(local.reclassAmount)#</strong> of this revenue due to existing write-offs.
			</cfif>
		</div>

		<cfif local.DisallowSplitEarliestDate>
			<div class="alert alert-danger">
				This revenue cannot be reclassed or split at this time because the earliest allowed reclass date is #DateFormat(local.earliestDate,"m/d/yyyy")#, which is in the future.
				It cannot be reclassed until that date.
				<br/><br/>
				The earliest reclass date for a revenue transaction is determined by several factors, including the Transaction Date of the revenue transaction, the earliest batch date for payments against this revenue, the most recent date of writeoff against this revenue, and the most recent date of voids against this revenue.
				<br/><br/>
				Contact Support with questions about the reclass process.
			</div>
		<cfelse>
			<table class="table table-sm table-borderless">
			<tr>
				<td width="25%">Amount to Reclass/Split:</td>
				<td><b>#DollarFormat(local.reclassAmount)#</b></td>
			</tr>
			<tr>
				<td>Current Revenue GL:</td>
				<td><b>#local.qryTransaction.glAccountPath# <cfif len(local.qryTransaction.AccountCode)>(#local.qryTransaction.AccountCode#)</cfif></b></td>
			</tr>
			<tr><td colspan="2"></td></tr>
			<tr>
				<td>Reclass/Split Date:</td>
				<td>
					<div class="input-group input-group-sm" style="width:200px;">
						<input type="text" name="reclassSplitDate" id="reclassSplitDate" value="#DateFormat(now(),'m/d/yyyy')#" class="form-control form-control-sm dateControl addReclassDateInput" autocomplete="off">
						<div class="input-group-append">
							<span class="input-group-text cursor-pointer calendar-button" data-target="reclassSplitDate"><i class="fa-solid fa-calendar"></i></span>
						</div>
					</div>
				</td>
			</tr>
			<tr>
				<td>Invoice Due Date:</td>
				<td>
					<div class="input-group input-group-sm" style="width:200px;">
						<input type="text" name="invoiceDueDate" id="invoiceDueDate" value="#DateFormat(now(),'m/d/yyyy')#" class="form-control form-control-sm dateControl addReclassDateInput" autocomplete="off">
						<div class="input-group-append">
							<span class="input-group-text cursor-pointer calendar-button" data-target="invoiceDueDate"><i class="fa-solid fa-calendar"></i></span>
						</div>
					</div>
				</td>
			</tr>
			<tr><td colspan="2"></td></tr>
			<tr>
				<td class="align-top" colspan="2">
					<table>
					<tbody id="tblDefReclassAmt">
						<tr>
							<td>
								<div class="input-group input-group-sm" style="width:200px;">
									<div class="input-group-prepend"><span class="input-group-text">$</span></div>
									<input type="text" name="reclassAmt_1_amt" id="reclassAmt_1_amt" value="#local.reclassAmount#" class="form-control form-control-sm reclassAmt" autocomplete="off">
								</div>
							</td>
							<td class="pl-2">to</td>
							<td>
								#local.strGLAcctWidget.html#
							</td>
							<td></td>
						</tr>
					</tbody>
					<tr>
						<td class="pt-3" colspan="4"><a href="javascript:addReclassRow();">Split Revenue into Another GL</a></td>
					</tr>
					</table>
				</td>
			</tr>
			</table>
		</cfif>
	</div>

	<div id="divReclassFormloadingDIV" class="d-none">
		<div class="mt-4 text-center">
			<div class="spinner-border" role="status"></div>
			<div class="font-weight-bold mt-2">Please wait while we save the reclassified amounts.</div>
		</div>
	</div>
	</form>
</div>

<script id="mc_reclassRow" type="text/x-handlebars-template">
	<tr id="reclassRow_{{nextRow}}">
		<td>
			<div class="input-group input-group-sm" style="width:200px;">
				<div class="input-group-prepend"><span class="input-group-text">$</span></div>
				<input type="text" name="reclassAmt_{{nextRow}}_amt" id="reclassAmt_{{nextRow}}_amt" value="0.00" class="form-control form-control-sm reclassAmt" autocomplete="off">
			</div>
		</td>
		<td class="pl-2">to</td>
		<td>
			<div id="glWidget_{{nextRow}}"></div>
		</td>
		<td>
			{{##compare nextRow '>' 0}}
				<a href="javascript:removeReclassRow({{nextRow}});"><i class="fa-solid fa-circle-minus text-darkred"></i></a>
			{{/compare}}
		</td>
	</tr>
</script>
</cfoutput>
