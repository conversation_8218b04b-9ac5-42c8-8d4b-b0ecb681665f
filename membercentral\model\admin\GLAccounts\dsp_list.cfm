<cfset local.urlString = "">
<cfset local.selectedTab = arguments.event.getTrimValue("tab","chart")>
<cfset local.lockTab = arguments.event.getTrimValue("lockTab","false") ? local.selectedTab : "">

<cfsavecontent variable="local.glAccountsJS">
	<cfoutput>
	<script language="javascript">
		var gridInitArray = new Array();
		gridInitArray["chart"] = false;
		gridInitArray["content"] = false;
		gridInitArray["auditlog"] = false;

		function onTabChangeHandler(ActiveTab) {
			if (!gridInitArray[ActiveTab.id]) {
				gridInitArray[ActiveTab.id] = true;
				switch(ActiveTab.id) {
					case "chart":
						initChartOfAccountsTable();
						break;
					case "content":
						initInvoiceContentTable();
						break;
					case "auditlog":
						initGLAAuditLogTable(); 
						break;
				}
			}
		}

		var chartOfAccountsTable, invoiceContentTable;

		function closeBox() { MCModalUtils.hideModal(); }
		function justReloadAccounts() { filterGLGrid(); }
		function reloadAccounts() { justReloadAccounts(); top.MCModalUtils.hideModal(); }
		function fnSaveAccountResult(stat) {
			if (stat == true) {
				MCModalUtils.hideModal();
				chartOfAccountsTable.draw();
			} else {
				justReloadAccounts();
				errorSavingGLAccount();
			}
		}
		function addNewGLAccount(atid) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: '',
				iframe: true,
				contenturl: '#this.link.edit#&glAccountID=0&retfn=fnSaveAccountResult&glatid=' + atid,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.validatefrmGLAccount',
					extrabuttonlabel: 'Save',
				}
			});
		}
		function exportCSV() {
			self.location.href='#this.link.exportcsv#';
		}
		function editGLAccount(glaid) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: '',
				iframe: true,
				contenturl: '#this.link.edit#&glAccountID=' + glaid + '&retfn=fnSaveAccountResult',
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.validatefrmGLAccount',
					extrabuttonlabel: 'Save Changes',
				}
			});
		}
		function errorSavingGLAccount() {
			$('.modal-backdrop').remove();
			var msg = '<div class="alert alert-warning m-2">Tips:<br/>&bull; Account Codes, if specified, must be unique across all GL Accounts.<br/>&bull; Account Names must be unique across accounts with the same parent GL Account.</div>';
			MCModalUtils.showModal({
					isslideout: true,
					modaloptions: {
						backdrop: 'static',
						keyboard: false
					},
					size: 'lg',
					title: 'There was an error saving the GL Account',
					iframe: false,
					strmodalbody: { 
						content: msg
					},
					strmodalfooter: {
						classlist: 'd-none',
						
					}
				});
		}
		function deleteGLAccount(glaid) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'md',
				title: 'Confirm Account Deletion',
				iframe: true,
				contenturl: '#this.link.confirmDeleteGLAccount#&glaid='+glaid,
				strmodalfooter: {
					classlist: 'text-right',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.fnDodeleteGLAccount',
					extrabuttonlabel: 'Ok'
				}
			});
		}
		function unDeleteGLAccount(glaid) {
			var unDeleteAccountResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true') {
					reloadAccounts(); 
				} else {
					var msg = 'There was an error restoring the GL Account. Contact MemberCentral for assistance.';
					alert(msg);
				}
			};
			var objParams = { GLAccountID:glaid };
			TS_AJX('ADMINGLACCT','unDeleteAccount',objParams,unDeleteAccountResult,unDeleteAccountResult,10000,unDeleteAccountResult);
		}
		function filterGLGrid() {
			chartOfAccountsTable.draw();
		}
		function displayAllGLs() {
			$('##kw').val('');
			filterGLGrid();
		}
		function reloadInvoiceMessages(){
			invoiceContentTable.draw();
		}
		function editInvoiceMessage(cid) {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'lg',
				title: (cid > 0 ? 'Edit' : 'Add') + ' End-of-Invoice Message',
				iframe: true,
				contenturl: '#this.link.editInvoiceMessage#&cid='+cid,
				strmodalfooter: {
					classlist: 'text-right',
					showclose: false,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmGLInvoiceMessage :submit").click',
					extrabuttonlabel: 'Save' + (cid > 0 ? ' Changes' : '')
				}
			});
			
		}
		function deleteInvoiceMessage(cid) {
			var deleteMessageResult = function(r) {
				if (r.success && r.success.toLowerCase() == 'true'){
					reloadInvoiceMessages();
				} else {
					delElement.removeClass('disabled').html('<i class="fa-solid fa-trash-can"></i>');
					var msg = 'There was an error deleting the End-of-Invoice Message.\nMessages cannot be deleted if they are associated to any existing invoice or with any revenue GL Account. Contact MemberCentral for assistance.';
					alert(msg);
				}
			};
			let delElement = $('##btnDel'+cid);
			mca_initConfirmButton(delElement, function(){
				var objParams = { contentid:cid };
				TS_AJX('ADMINGLACCT','deleteInvoiceMessage',objParams,deleteMessageResult,deleteMessageResult,10000,deleteMessageResult);		
			});
		}
		function toggleParentDisplay(rowID) {
			let rowToggleBtn = $('##chartOfAccountsTable ##displayLabel_'+rowID+' i.rowToggleBtn');
			rowToggleBtn.toggleClass('fa-folder-plus fa-folder-minus');
			toggleChildRows(rowID, rowToggleBtn.hasClass('fa-folder-minus'));
		}
		function toggleChildRows(rowID,f) {
			$('##chartOfAccountsTable tr.child-of-'+rowID).toggleClass('d-none',!f).each(function(i,thisRow) {
				if ($(this).find('i.rowToggleBtn').hasClass('fa-folder-minus')) toggleChildRows($(this).attr('id'),f);
			});
		}
		
		function initInvoiceContentTable() {
			invoiceContentTable = $('##invoiceContentTable').DataTable({
				"processing": true,
				"serverSide": true,
				"pageLength": 50,
				"language": {
					"lengthMenu": "_MENU_"
				},
				"ajax": { 
					"url": "#local.invoiceContentListLink#",
					"type": "post"
				},
				"autoWidth": false,
				"columns": [
					{ "data": "contenttitle", "className": "align-top", "width": "80%" },
					{
						"data": null,
						"render": function (data, type) {
							let renderData = '';
							if (type === 'display') {
								renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1" title="Edit Message" onclick="editInvoiceMessage('+data.contentid+');return false;"><i class="fas fa-pencil"></i></a>';
								renderData += '<a href="##" class="btn btn-xs text-danger p-1 mx-1'+ (!data.candelete ? ' invisible' : '') +'" '+ (data.candelete ? 'id="btnDel'+data.contentid+'" onclick="deleteInvoiceMessage('+data.contentid+');return false;" title="Delete Message"' : '') +'><i class="fa-solid fa-trash-can"></i></a>';
							}
							return type === 'display' ? renderData : data;
						},
						"width": "20%",
						"className": "align-top text-center",
						"orderable": false
					}
				],
				"order": [[0, 'asc']],
				"searching": false
			});
		}
		function initChartOfAccountsTable() {
			chartOfAccountsTable = $('##chartOfAccountsTable').DataTable({
				"processing": true,
				"serverSide": true,
				"paging": false,
				"info": false,
				"ajax": { 
					"url": "#local.resultsList#",
					"type": "post",
					"data": function(d) {
						d['kw'] = $('##kw').val() || '';
					}
				},
				"autoWidth": false,
				"columns": [
					{ "data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							let thisRowID = data['DT_RowId'];
							if (type === 'display')	{
								let cellPadding = data.level > 1 ? 24 * (data.level-1) : 0;
								accountName = data.accountName;
								if(data.status == 'I'){
									accountName = '<i>'+data.accountName+'</i>';
								}else{
									if(data.isSystemAccount == 1 && data.GLAccountID == 0){
										accountName = '<b>'+data.accountName+'</b>';
									}
								}
								profileName = "";
								if(data.profileName.length > 0){
									profileName = '<div><i class="fas fa-folder-tree fa-fw pr-2 invisible"></i><span class="text-dim small">'+data.profileName+'</span></div>';
								}

								if (data.hasChildren) {
									renderData += '<a href="javascript:toggleParentDisplay(\''+thisRowID+'\');" id="displayLabel_'+thisRowID+'" style="padding-left:'+cellPadding+'px;"><i class="fas fa-folder-minus fa-fw rowToggleBtn pr-2"></i> '+ accountName +'</a>';
								} else {
									renderData += '<div style="padding-left:'+cellPadding+'px;"><i class="fas fa-folder-tree fa-fw pr-2 invisible"></i>'+ accountName +profileName+'</div>';
								}
							}
							return type === 'display' ? renderData : data;
						},
						"width": "65%"
					},
					{ 	
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display')	{
								renderData = (data.status == 'I')? '<i>'+data.accountCode+'</i>' : data.accountCode;
							}
							return type === 'display' ? renderData : data;
						},
						"width": "20%"
					},
					{ 	
						"data": null,
						"render": function ( data, type, row, meta ) {
							let renderData = '';
							if (type === 'display')	{
								if(data.isSystemAccount == 1 && data.GLAccountID == 0){
										renderData += '<a href="##" class="btn btn-xs text-success p-1 mx-1'+(data.allowAddAccounts ? '' : ' invisible')+'" '+(data.allowAddAccounts ? 'onclick="addNewGLAccount('+data.accountTypeID+');return false;" title="Add Account"' : '')+'><i class="far fa-circle-plus"></i></a>';
										renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-map-marker-alt"></i></a>';
										renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-pencil"></i></a>';
										renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-trash-alt"></i></a>';
								}
								else {
									renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="far fa-circle-plus"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1'+(data.accountType == 'Revenue' ? '' : ' invisible')+'" '+(data.accountType == 'Revenue' ? 'onclick="showGLAccountUsage('+data.GLAccountID+',\'groupUsage\');return false;" title="View GL Account Usage"' : '' )+'><i class="fas fa-map-marker-alt"></i></a>';
									renderData += '<a href="##" class="btn btn-xs text-primary p-1 mx-1'+(data.canEdit ? '' : ' invisible')+'" '+(data.canEdit ? 'onclick="editGLAccount('+data.GLAccountID+');return false;" title="Edit this GL Account"' : '')+'><i class="fas fa-pencil"></i></a>';
									if(data.canDelete){
										renderData += '<a href="##" class="btn btn-xs text-danger p-1 mx-1" onclick="deleteGLAccount('+data.GLAccountID+');return false;" title="Delete this GL Account"><i class="fas fa-trash-alt"></i></a>';
									}
									else if(data.canUnDelete){
										renderData += '<a href="##" class="btn btn-xs text-success p-1 mx-1" onclick="unDeleteGLAccount('+data.GLAccountID+');return false;" title="Restore this GL Account"><i class="fas fa-undo"></i></a>';
									}
									else renderData += '<a href="##" class="btn btn-xs p-1 mx-1 invisible"><i class="fas fa-trash-alt"></i></a>';
								}
							}
							return type === 'display' ? renderData : data;
						},
						"className": "text-center",
						"width": "15%"
					}
				],
				"searching": false,
				"ordering": false,
				createdRow: function (row, data, index) {
					$(row).attr('data-accountname',data.accountName);
				}
			});
		}
		function showGLAccountUsage(glid) {
			MCModalUtils.showModal({
				isslideout: true,
				size: 'xl',
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				title: $('##gla'+glid).data('accountname'),
				iframe: true,
				contenturl: '#this.link.viewGLAccountUsage#&glid='+glid,
				strmodalfooter : {
					classlist: 'text-right',
					showclose: true
				}
			});
		}
		function massEditGLQBCols() {
			MCModalUtils.showModal({
				isslideout: true,
				modaloptions: {
					backdrop: 'static',
					keyboard: false
				},
				size: 'xl',
				title: 'Mass Edit QuickBooks Online Mappings',
				iframe: true,
				contenturl: '#local.massEditGLQBColsLink#',
				strmodalfooter : {
					classlist: 'd-flex',
					showclose: true,
					showextrabutton: true,
					extrabuttonclass: 'btn-primary ml-auto',
					extrabuttononclickhandler: '$("##MCModalBodyIframe")[0].contentWindow.$("##frmMassEditGLQBCols :submit").click',
					extrabuttonlabel: 'Save',
				}
			});
		}

		$(function() {
			mca_initNavPills('GLAccountAdminPills', '#local.selectedTab#', '#local.lockTab#', onTabChangeHandler);
		});
	</script>
	</cfoutput>
</cfsavecontent>
<cfhtmlhead text="#application.objCommon.minText(local.glAccountsJS)#">

<cfoutput>
<h4 class="mb-2">Chart of Accounts</h4>

<ul class="nav nav-pills nav-pills-dotted" id="GLAccountAdminPills">
	<cfset local.thisTabID = "chart">
	<cfset local.thisTabName = "chart">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Chart of Accounts</a>
	</li>
	<cfset local.thisTabID = "content">
	<cfset local.thisTabName = "content">
	<li class="nav-item">
		<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">End-of-Invoice Messages</a>
	</li>
	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<cfset local.thisTabID = "auditlog">
		<cfset local.thisTabName = "auditlog">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Audit Log</a>
		</li>

		<cfset local.thisTabID = "import">
		<cfset local.thisTabName = "import">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Chart of Accounts Import</a>
		</li>
		<cfset local.thisTabID = "importExport">
		<cfset local.thisTabName = "ex">
		<li class="nav-item">
			<a class="nav-link" id="#local.thisTabID#" data-toggle="pill" href="##pills-#local.thisTabID#" role="tab" aria-controls="pills-#local.thisTabID#" aria-selected="false" data-tabname="#local.thisTabName#">Import / Export</a>
		</li>
	</cfif>
</ul>

<div class="tab-content mc_tabcontent p-2 pb-0 card card-box shadow-none" id="pills-tabContent">
	<div class="tab-pane fade p-2" id="pills-chart" role="tabpanel" aria-labelledby="chart">
		<div class="toolButtonBar">
			<div><a href="javascript:exportCSV();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="" data-original-title="Click to export."><i class="fa-light fa-file-export"></i> Export CSV</a></div>
			<div><a href="javascript:massEditGLQBCols();" data-tooltip-class="tooltip-primary" data-toggle="tooltip" data-placement="top" data-trigger="hover" title="" data-original-title="Click to Mass Edit QuickBooks Online Mappings."><i class="fa-light fa-file-pen"></i> Mass Edit QuickBooks Online Mappings</a></div>
		</div>

		<div class="card card-box my-2">
			<div class="card-header py-1 bg-light">
				GL Accounts Search
			</div>
			<div class="card-body pb-3">
				<form name="frmGLSearch" id="frmGLSearch" onsubmit="filterGLGrid(); return false;">
					<label for="kw" class="mr-1">Keywords:</label>
					<input type="text" size="45" id="kw" name="kw" class="mr-2" value="">

					<button type="submit" name="btnSearch" class="btn btn-sm btn-primary mr-1">Search</button>
					<button type="button" name="btnClear" class="btn btn-sm btn-secondary" onclick="displayAllGLs();">Display All</button>
				</form>
			</div>
		</div>
		
		<table id="chartOfAccountsTable" class="table table-sm table-hover table-bordered" style="width:100%">
			<thead>
				<tr>
					<th>GL Account</th>
					<th>Account Code</th>
					<th>Actions</th>
				</tr>
			</thead>
		</table>
		<div class="font-size-sm text-dim">
			<span>Accounts <i>in italics</i> are inactive and cannot accept new transactions.</span>
		</div>
	</div>

	<div class="tab-pane fade" id="pills-content" role="tabpanel" aria-labelledby="content">
		<div class="row align-items-end mb-3">
			<div class="col-md">
				Messages appear at the end of invoices based on the revenue GL Accounts on the particular invoice.<br />
				Messages cannot be deleted if they are associated to any existing invoice or with any revenue GL Account.
			</div>
			<div class="col-md-auto">
				<button type="button" class="btn btn-sm btn-secondary" onclick="editInvoiceMessage();">
					<span class="btn-wrapper--icon"><i class="fa-regular fa-circle-plus"></i></span>
					<span class="btn-wrapper--label">Create Message</span>
				</button>
			</div>
		</div>
		<table id="invoiceContentTable" class="table table-sm table-striped table-bordered" style="width:100%">
			<thead>
				<tr>
					<th>Message</th>
					<th>Actions</th>
				</tr>
			</thead>
		</table>
	</div>

	<cfif application.objUser.isSuperUser(cfcuser=session.cfcuser)>
		<div id="pills-auditlog" class="tab-pane fade" role="tabpanel" aria-labelledby="auditlog">
			<cfinclude template="dsp_GLAccount_auditLog.cfm">
		</div>
		<div class="tab-pane fade" id="pills-import" role="tabpanel" aria-labelledby="import">
			<cfif local.showImpExTemplate eq 1>
				<cfinclude template="dsp_Accountimport.cfm">
			<cfelse>
				<cfoutput>#local.impExData#</cfoutput>
			</cfif>
		</div>
		<div class="tab-pane fade" id="pills-importExport" role="tabpanel" aria-labelledby="importExport">
			<cfinclude template="dsp_importexport.cfm">
		</div>
	</cfif>
</div>
</cfoutput>