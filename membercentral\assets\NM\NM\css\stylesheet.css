@charset "utf-8";

body {font-size:12px; margin: 0; padding: 0; font-family: Arial,helvetica,sans-serif; color: #333; line-height: 25px;    background: #c4c7ff;
}
*, input[type="search"] { -moz-box-sizing: border-box; -ms-box-sizing: border-box; -o-box-sizing: border-box; -webkit-box-sizing: border-box; box-sizing: border-box; }
img { max-width: 100%; }
a { color: #08c; text-decoration: none; }
h1, h2, h3, h4, h5, h6 { color: #383a39; }
a:hover .BodyText, .BodyText a:hover, a:hover, a:focus { color: #005580; text-decoration: none }
.InnerContainer { max-width:960px;width: 100%; margin:0 auto; }
p { font-size:12px; line-height: 25px; }
.clearfix::before, .clearfix::after { content: ""; display: table; width: 100%; clear: both; }
.wrapper .container{
	background: #fff;
}
/***Header***/
.header{position:relative;width:100%;
    background: #0b0a43;    border-bottom: 2px solid white;}
.header .container{max-width:1400px; width: 100%;background: url(../images/headerbg.jpg) top center no-repeat; height:380px;}
.newheader header {border: none; }
.top-header a small {
    font-size: 14px;
    color: #fff;
    margin-left: 5px;
}

.top-header-in {
    padding: 28px 10px;
    display: flex;
    align-items: center;
    width: 100%;
    justify-content: space-between;
}
.topLinksMenu {
    display: flex;
    align-items: center;
    gap: 30px;
}

.topLinksMenu span {
    display: inline-block;
}

.topLinksMenu .topLink {
    color: #fff;
    text-decoration: none;
    font-family: Arial;
    font-size: 13px;
    display: inline-flex;
    align-items: center;
    gap: 5px;
}

.topLinksMenu .topLink:hover {
    color: #d3e8f0;
    text-decoration: none;
}

.topLinksMenu .topLink i {
    font-size: 14px;
}

#searchbox {
    margin-bottom: 0;
}
#searchArea input.searchField {
    background: url(../images/searchIco.png) no-repeat;
    background-color: rgba(0, 0, 0, 0);
    background-color: #fff;
    border: 1px solid #adb4bb;
    color: #163458;
    font-family: Arial;
    font-size: 13px;
    line-height: 120%;
    margin-top: 0;
    padding: 5px;
    padding-left: 24px;
    width: 193px;
    height: 28px;
    border-radius: 0;
    margin-bottom: 0;
}
#searchArea button.searchButton {
    vertical-align: middle;
    border: 1px solid #adb4bb;
    border-left: 0px;
    font-size: 13px;
    font-family: Arial;
    color: #163458;
    padding: 0px 5px 0px 5px;
    background: #d3e8f0;
    margin: 0;
    margin-left: -5px;
    vertical-align: top;
    height: 28px;
    line-height: 28px;
}

.bottom-header {
    background: #fff;
    padding: 18px 0 0;
}





#cssmenu,#cssmenu ul,#cssmenu ul li,#cssmenu ul li a,#cssmenu #head-mobile{margin: 0px; border:0;list-style:none;line-height:1;display:block;position:relative;-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
#cssmenu:after,#cssmenu > ul:after{content:".";display:block;clear:both;visibility:hidden;line-height:0;height:0}
#cssmenu #head-mobile{display:none}
#cssmenu{background:#fff;padding-top:4px;}
#cssmenu > ul > li{float:left; width:auto;    padding: 10px 10px 5px;}
#cssmenu > ul > li > a{color: #163458;
    display: block;
    font-size: 15px;
    padding: 15px 20px 15px 20px;
    text-decoration: none;
    border-radius: 5px 5px;
    -moz-border-radius: 5px 5px;
    -webkit-border-radius: 5px 5px;}
#cssmenu > ul > li > a:hover{ color: #fff;
    background: #163458;}


#cssmenu > ul > li:hover,#cssmenu ul li.active:hover,#cssmenu ul li.active,#cssmenu ul li.has-sub.active:hover{-webkit-transition:background .3s ease;-ms-transition:background .3s ease;transition:background .3s ease;}
#cssmenu > ul > li:last-child> a { border-right: none;}
#cssmenu > ul > li.has-sub:hover > a:before{top:23px;height:0}
#cssmenu ul ul{position:absolute;left:-9999px}
#cssmenu ul ul li{height:0;-webkit-transition:all .25s ease;-ms-transition:all .25s ease;background: #163458;transition:all .25s ease}
#cssmenu ul ul li:hover{}
#cssmenu li:hover > ul{left:auto;top: 84%;background: #163458;padding: 5px; border-radius:5px; z-index:2;}
#cssmenu li:hover > ul > li{height:auto;padding: 0px 15px;}
#cssmenu ul ul ul{margin-left:100%;top:0}
#cssmenu ul ul li a{border-bottom:0px solid rgba(150,150,150,0.15);padding:11px 15px;width:200px;font-size:15px;text-decoration:none;color:#fff;font-weight:400; border-radius: 5px;}
#cssmenu ul ul li:last-child > a,#cssmenu ul ul li.last-item > a{border-bottom:0}
#cssmenu ul ul li:hover > a,#cssmenu ul ul li a:hover{background: #fff;color: #3D5B77;}
#cssmenu ul ul li.has-sub > a:after{position:absolute;top:16px;right:11px;width:8px;height:2px;display:block;background:#ddd;content:''}
#cssmenu ul ul li.has-sub > a:before{position:absolute;top:13px;right:14px;display:block;width:2px;height:8px;background:#ddd;content:'';-webkit-transition:all .25s ease;-ms-transition:all .25s ease;transition:all .25s ease}
#cssmenu ul ul > li.has-sub:hover > a:before{top:17px;height:0}
#cssmenu ul ul li.has-sub:hover,#cssmenu ul li.has-sub ul li.has-sub ul li:hover{background:#363636;}
#cssmenu ul ul ul li.active a{border-left:1px solid #333}
#cssmenu > ul > li.has-sub > ul > li.active > a,#cssmenu > ul ul > li.has-sub > ul > li.active> a{border-top:1px solid #333}



/******Footer*****/
.footer {
    background-color: #c4c7ff;
    color: #fff;
    text-align: center;
    padding: 10px 0 4px 0;
    position: relative;
}

.footer-top ul {
    list-style: none;
    color: #ccc;
    margin: auto auto;
    padding: 0;
    display: flex;
    justify-content: space-between;
}
.footer-top ul li {
    padding: 5px 15px;
}

.footer-top ul a {
    color: #163458;
    display: block;
    font-size: 14px;
    padding: 10px 20px 10px 20px;
    text-decoration: none;
    border-radius: 5px 5px;
    -moz-border-radius: 5px 5px;
    -webkit-border-radius: 5px 5px;
}

.footer-top ul a:hover {
    color: #fff;
    background: #163458;
}
.footer-bottom {
    margin-top: -5px;
}
.footer-bottom .InfoText {
    color: #fff;
        font-family: arial;
    font-size: 10px;
}

.footer-bottom .InfoText a{text-transform: uppercase; color: #4b53ff; text-decoration: underline;}

/******Content*****/
.content { padding:15px; background:#fff; }
.zonewrapper form { margin: 0; }
.template input { height: auto; }
.template .form-horizontal .controls span.input-xlarge.uneditable-input { height: auto; }
.nav-pills>li>a , .nav-list>li>a{
    text-decoration: none;
}
.nav-pills>.active>a, .nav-pills>.active>a:hover, .nav-pills>.active>a:focus {
    background-color: #642c8a;
}
.nav-list>.active>a, .nav-list>.active>a:hover, .nav-list>.active>a:focus {
    background: #642c8a;
}
.quicklinks h3 {
    line-height: 1;
    border-bottom: 1px solid #ccc;
    padding: 0 0 10px;
    margin: 0 0 14px;
}
.quicklinks-menu {
    margin: 0 0 20px;
}
.quicklinks-menu li {
    list-style: none; text-align:center;
}
.quicklinks-menu li a {
    background: #642C8A;
    display: block;
    color: #fff;
    text-decoration: none !important;
    padding: 11px;
    border-bottom: 1px solid #ffffff;
        text-transform: uppercase; border-radius:3px; -webkit-border-radius:3px; -moz-border-radius:3px; -ms-border-radius:3px;
}
.quicklinks-menu li a:hover { opacity:0.8;
}
.dotSpan{
	font-size: 25px;
    vertical-align: text-bottom;
    font-weight: 500;
}
.menuWrap li.has-sub:last-child  ul:last-child ul:last-child{
    left: auto;
    right: 100%;
    top: 0 !important;
}