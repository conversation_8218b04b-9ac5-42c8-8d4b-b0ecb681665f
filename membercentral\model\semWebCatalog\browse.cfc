<cfcomponent output="no">

	<cffunction name="getSWPrograms" access="public" output="false" returntype="struct">
		<cfargument name="siteID" type="numeric" required="true">
		<cfargument name="catalogOrgCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="true">
		<cfargument name="depoMemberDataID" type="numeric" required="true">
		<cfargument name="qrySWP" type="query" required="true">
		<cfargument name="strAssociation" type="struct" required="true">
		<cfargument name="swRegCart" type="query" required="true">
		<cfargument name="strFilters" type="struct" required="true">
		<cfargument name="mode" type="string" required="true" hint="browse, featured, myFeatured, mergecode">

		<cfset var local = structNew()>

		<cfswitch expression="#arguments.mode#">
			<cfcase value="browse">
				<cfset local.strReturn = { 
					"arrPrograms":[],
					"strCarousel":{},
					"arrCreditsInfo":getCreditsInfoForBrowsePrograms(catalogOrgCode=arguments.catalogOrgCode),
					"qryCategories":CreateObject("component","model.seminarweb.SWCategories").getCategoriesForCatalogSearch(catalogOrgCode=arguments.catalogOrgCode),
					"arrAuthors":getAuthorsForBrowsePrograms(catalogOrgCode=arguments.catalogOrgCode),
					"qryPublishers":CreateObject("component","model.seminarweb.SWParticipants").getPublishersForCatalogSearch(catalogOrgCode=arguments.catalogOrgCode),
					"strPagination":{ "startPos":arguments.strFilters.startPos, "count":25, "totalCount":0, "numTotalPages":0, "numCurrentPage":0, "startPage":1, "endPage":1, "maxPage":5 }
					}>

				<cfif val(arguments.qrySWP.browseCarouselID)>
					<cfset local.strReturn.strCarousel = createObject("component","model.carousel.carousel").renderWebsiteCarousel(siteID=arguments.siteID, carouselID=arguments.qrySWP.browseCarouselID)>
				</cfif>
			</cfcase>
			<cfcase value="featured,myFeatured,mergecode">
				<cfset local.strReturn = { "arrPrograms":[] }>
			</cfcase>
		</cfswitch>

		<cfset local.searchTerms = "">
		<cfif len(arguments.strFilters.keywordsList)>
			<cfset local.searchTerms = createObject("component","model.search.bucket").prepareSearchString(stringToClean=arguments.strFilters.keywordsList.replaceAll('~',' '))>
			<cfif arguments.qrySWP.isConf>
				<cfset local.evSearchTerms = local.searchTerms & " AND ( mcsearchSiteCode#arguments.catalogOrgCode#xxx OR mcSiteCode#arguments.catalogOrgCode#xxx )">
			</cfif>
		</cfif>

		<cfset local.includeConf = arguments.qrySWP.isConf is 1 and arguments.strFilters.formats.listFindNoCase("conf")>
		<cfset local.includeSWL = arguments.qrySWP.isSWL is 1 and arguments.strFilters.formats.listFindNoCase("swl")>
		<cfset local.includeSWOD = arguments.qrySWP.isSWOD is 1 and arguments.strFilters.formats.listFindNoCase("swod")>
		<cfset local.includeSWB = arguments.strFilters.formats.listFindNoCase("swb")>

		<cfsavecontent variable="local.defaultOrderBy">
			<cfoutput>
			tmp.isFeatured desc, 
			<cfif local.includeConf OR local.includeSWL>
				case when tmp.ft IN ('SWL','EV') <cfif local.includeSWB>OR (tmp.ft = 'SWB' and tmp.isSWODBundle = 0)</cfif> then tmp.dateStart else @maxDateStartPlusTwo end asc, 
			</cfif>
			<cfif local.includeSWOD>
				case when tmp.ft = 'SWOD' <cfif local.includeSWB>OR (tmp.ft = 'SWB' and tmp.isSWODBundle = 1)</cfif> then tmp.dateActivated else @nowDate end desc, 
			</cfif>
			case when tmp.ft = 'SWL' then 1 
				when tmp.ft = 'SWB' and tmp.isSWODBundle = 0 then 2  
				when tmp.ft = 'EV' then 3 
				when tmp.ft = 'SWOD' then 4
				when tmp.ft = 'SWB' and tmp.isSWODBundle = 1 then 5
				else null end asc,
			tmp.programName asc
			</cfoutput>
		</cfsavecontent>

		<cfswitch expression="#arguments.strFilters.sortoption#">
			<cfcase value="date">
				<cfset local.orderBy = local.defaultOrderBy>
			</cfcase>
			<cfcase value="nameasc">
				<cfset local.orderBy = "tmp.isFeatured desc, tmp.programName asc">
			</cfcase>
			<cfcase value="namedesc">
				<cfset local.orderBy = "tmp.isFeatured desc, tmp.programName desc">
			</cfcase>
			<cfcase value="rank">
				<cfset local.orderBy = "tmp.isFeatured desc, tmp.rank desc">
			</cfcase>
			<cfdefaultcase>
				<cfset local.orderBy = local.defaultOrderBy>
			</cfdefaultcase>
		</cfswitch>

		<cfquery name="local.qryGetParticipantID" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SELECT dbo.fn_getParticipantIDFromOrgcode(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.catalogOrgCode#">) as participantID
		</cfquery>

		<cfset local.isCurrentSiteInParticipantFilters = true>
		<cfif listLen(arguments.strFilters.participantIDList) AND NOT listFind(arguments.strFilters.participantIDList,local.qryGetParticipantID.participantID)>
			<cfset local.isCurrentSiteInParticipantFilters = false>
		</cfif>
		
		<!--- if there is a participantID filter, and the current site is not in that filter, then events should never show so dont even get that data --->
		<cfif local.includeConf AND NOT local.isCurrentSiteInParticipantFilters>
			<cfset local.includeConf = false>
		</cfif>

		<cfquery name="local.qryPrograms" datasource="#application.dsn.tlasites_seminarweb.dsn#" result="local.qryProgramsResult">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			IF OBJECT_ID('tempdb..##tmpSWPrograms') IS NOT NULL 
				DROP TABLE ##tmpSWPrograms;
			IF OBJECT_ID('tempdb..##tmpFilteredSWPrograms') IS NOT NULL 
				DROP TABLE ##tmpFilteredSWPrograms;
			CREATE TABLE ##tmpSWPrograms (programID int, programName varchar(250), programSubTitle varchar(250), programDesc varchar(max), 
				programLocation varchar(200), enrollmentID int, isRegistered bit, dateStart datetime, 
				dateEnd datetime, displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
				displayTimeZoneAbbr varchar(4), wddxTimeZones varchar(max), isAllDayEvent bit, dateActivated date, publisherOrgCode varchar(10), 
				linkedSWODSeminarID int, freeRateDisplay varchar(5), isSWODBundle bit, featureImageSiteCode varchar(10), featureImageOrgCode varchar(10), 
				featureImageID int, featureImageFileExtension varchar(10), featureImageSizeID int, isFeatured bit, isSaved bit, ft varchar(4), 
				itemType varchar(15), allowRegistrants bit, altRegistrationURL varchar(300), [rank] int, rowNum int,
				INDEX IX_tmpSWPrograms__isSWODBundle__ft__programID NONCLUSTERED (isSWODBundle, ft, programID),
				INDEX IX_tmpSWPrograms__programID__ft__programName__dateStart__dateActivated__isSWODBundle__isFeatured NONCLUSTERED (programID, ft, programName, dateStart, dateActivated, isSWODBundle, isFeatured)
				);
			CREATE TABLE ##tmpFilteredSWPrograms (programID int, ft varchar(4));

			DECLARE @catalogOrgCode varchar(10), @participantID int, @memberID int, @depoMemberDataID int, @posStart int, @posStartPlusCount int, 
				@totalCount int, @nowDate datetime = GETDATE(), @categoryIDList varchar(max), @calendarID int, @groupPrintID int,
				@swFtdImageSizeReferenceType varchar(30), @savedKey uniqueidentifier;
			SET @catalogOrgCode = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.catalogOrgCode#">;
			SET @participantID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.qryGetParticipantID.participantID#">;
			SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;
			SET @depoMemberDataID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.depoMemberDataID#">;
			<cfswitch expression="#arguments.mode#">
				<cfcase value="browse">
					SET @posStart = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strReturn.strPagination.startPos#">;
					SET @posStartPlusCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#local.strReturn.strPagination.count#">;
					SET @swFtdImageSizeReferenceType = 'swProgramListings';
				</cfcase>
				<cfcase value="featured,myFeatured">
					SET @swFtdImageSizeReferenceType = 'swFeaturedProgramLanding';
				</cfcase>
				<cfcase value="mergecode">
					SET @posStart = 1;
					SET @posStartPlusCount = @posStart + <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.strFilters.maxrows#">;
					SET @swFtdImageSizeReferenceType = 'swProgramListings';
				</cfcase>
			</cfswitch>
			<cfif structKeyExists(cookie,"SWBROWSE") AND isValid('guid',cookie.SWBROWSE)>
				SET @savedKey = <cfqueryparam cfsqltype="CF_SQL_IDSTAMP" value="#cookie.SWBROWSE#">;
			</cfif>

			<cfif len(local.searchterms)>
				DECLARE @swKeywords varchar(4000) = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.searchterms#">;

				IF OBJECT_ID('tempdb..##swFulltext') IS NOT NULL 
					DROP TABLE ##swFulltext;
				CREATE TABLE ##swFulltext (id int, rank int, programID int, programType varchar(4));

				INSERT INTO ##swFulltext (id, [rank])
				select [key], [rank]
				from containstable(searchMC.dbo.sw_programs, searchtext, @swKeywords);

				IF @@ROWCOUNT > 0
					UPDATE swFT
					SET swFT.programID = s.programID,
						swFT.programType = s.programType
					FROM ##swFulltext as swFT
					INNER JOIN searchMC.dbo.sw_programs as s on s.id = swFT.id;

				<cfif local.includeConf>
					INSERT INTO ##swFulltext (id, [rank], programType)
					select [key], [rank], 'EV'
					from containstable(searchMC.dbo.ev_events, searchtext, @swKeywords);

					IF @@ROWCOUNT > 0
						UPDATE swFT
						SET swFT.programID = s.eventID
						FROM ##swFulltext as swFT
						INNER JOIN searchMC.dbo.ev_events as s on s.id = swFT.id
						WHERE swFT.programType = 'EV';
				</cfif>
			</cfif>

			<cfif listLen(arguments.strFilters.subjects)>
				DECLARE @swSubjectAreas TABLE (categoryID int PRIMARY KEY);

				INSERT INTO @swSubjectAreas (categoryID)
				select distinct c.categoryID
				from dbo.tblCategories as c
				inner join memberCentral.dbo.fn_varcharListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFilters.subjects#">,',') as tmp on membercentral.dbo.fn_regexReplace(c.CategoryName,'[^A-Za-z0-9]','') = tmp.listitem;

				<cfif local.includeConf>
					DECLARE @evCategories TABLE (categoryID int PRIMARY KEY, calendarID int);

					INSERT INTO @evCategories (categoryID, calendarID)
					select distinct c.categoryID, c.calendarID
					from dbo.tblParticipantEvents as pe
					inner join memberCentral.dbo.ev_categories as c on c.categoryID = pe.categoryID
						and pe.participantID = @participantID
					inner join memberCentral.dbo.fn_varcharListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFilters.subjects#">,',') as tmp on tmp.listitem = membercentral.dbo.fn_regexReplace(c.category,'[^A-Za-z0-9]','');
				</cfif>
			</cfif>

			<cfif listLen(arguments.strFilters.participantIDList)>
				DECLARE @swPublishers TABLE (participantOrgCode varchar(10) PRIMARY KEY);

				INSERT INTO @swPublishers (participantOrgCode)
				select distinct p.orgCode
				from memberCentral.dbo.fn_intListToTableInline(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFilters.participantIDList#">,',') as tmp
				inner join dbo.tblParticipants as p on p.participantID = tmp.listitem;
			</cfif>

			<cfif local.includeSWL>
				IF OBJECT_ID('tempdb..##tmp_SWBrowse_SWL') IS NOT NULL 
					DROP TABLE ##tmp_SWBrowse_SWL;
				CREATE TABLE ##tmp_SWBrowse_SWL (seminarID int PRIMARY KEY);

				INSERT INTO ##tmp_SWBrowse_SWL (seminarID)
				SELECT seminarID
				FROM dbo.swl_SeminarsInMyCatalog(@catalogOrgCode,'1-1-2005',DATEADD(yy,2,@nowDate));
			</cfif>

			<cfif local.includeSWL OR local.includeSWOD>
				IF OBJECT_ID('tempdb..##tmp_SWBrowse_SWOD') IS NOT NULL 
					DROP TABLE ##tmp_SWBrowse_SWOD;
				CREATE TABLE ##tmp_SWBrowse_SWOD (seminarID int PRIMARY KEY);

				INSERT INTO ##tmp_SWBrowse_SWOD (seminarID)
				SELECT seminarID
				FROM dbo.swod_SeminarsInMyCatalog(@catalogOrgCode);
			</cfif>

			<cfif local.includeSWB>
				IF OBJECT_ID('tempdb..##tmp_SWBrowse_SWB') IS NOT NULL 
					DROP TABLE ##tmp_SWBrowse_SWB;
				CREATE TABLE ##tmp_SWBrowse_SWB (bundleID int PRIMARY KEY);

				INSERT INTO ##tmp_SWBrowse_SWB (bundleID)
				SELECT bundleID
				FROM dbo.swb_BundlesInMyCatalog(@catalogOrgCode);
			</cfif>

			<cfif listLen(arguments.strFilters.authorIDList)>
				DECLARE @speakerAuthorTypeID int;
				DECLARE @swFilteredSpeakers TABLE (authorID int PRIMARY KEY);
				DECLARE @swProgramSpeakers TABLE (authorID int, programID int, ft varchar(4));

				SELECT @speakerAuthorTypeID = authorTypeID
				FROM dbo.tblAuthorTypes
				WHERE authorType = 'Speaker';

				INSERT INTO @swFilteredSpeakers (authorID)
				select distinct a.authorID
				from memberCentral.dbo.fn_intListToTable(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.strFilters.authorIDList#">,',') as tmp
				inner join dbo.tblAuthors as a on a.authorID = tmp.listitem
				where a.authorTypeID = @speakerAuthorTypeID;

				<cfif local.includeSWL>
					INSERT INTO @swProgramSpeakers (authorID, programID, ft)
					SELECT distinct a.authorID, smc.seminarID, 'swl'
					FROM @swFilteredSpeakers AS a 
					INNER JOIN dbo.tblSeminarsAndAuthors AS saa ON a.authorID = saa.authorID 
					INNER JOIN ##tmp_SWBrowse_SWL AS smc ON smc.seminarID = saa.seminarID
					<cfif local.includeSWB>
							UNION
						SELECT distinct a.authorID, bmc.bundleID, 'swb'
						FROM ##tmp_SWBrowse_SWB AS bmc
						INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
						INNER JOIN ##tmp_SWBrowse_SWL AS smc ON smc.seminarID = bi.seminarID
						INNER JOIN dbo.tblSeminarsAndAuthors AS saa ON saa.seminarID = smc.seminarID 
						INNER JOIN @swFilteredSpeakers AS a on a.authorID = saa.authorID;
					</cfif>
				</cfif>

				<cfif local.includeSWOD>
					INSERT INTO @swProgramSpeakers (authorID, programID, ft)
					SELECT distinct a.authorID, smc.seminarID, 'swod'
					FROM dbo.tblSeminarsAndAuthors AS saa 
					INNER JOIN @swFilteredSpeakers AS a ON a.authorID = saa.authorID 
					INNER JOIN ##tmp_SWBrowse_SWOD AS smc ON smc.seminarID = saa.seminarID
					<cfif local.includeSWB>
							UNION
						SELECT distinct a.authorID, bmc.bundleID, 'swb'
						FROM ##tmp_SWBrowse_SWB AS bmc
						INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = bmc.bundleID
						INNER JOIN dbo.tblSeminars as s on s.seminarID = bi.seminarID
						INNER JOIN dbo.tblSeminarsSWOD as sod on sod.seminarid = s.seminarid
						INNER JOIN dbo.tblSeminarsAndAuthors AS saa ON saa.seminarID = s.seminarID 
						INNER JOIN @swFilteredSpeakers AS a on a.authorID = saa.authorID
						WHERE s.isPublished = 1
						AND s.isDeleted = 0
						AND s.participantID = @participantID;
					</cfif>
				</cfif>
			</cfif>

			<!--- events --->
			<cfif local.includeConf>
				IF OBJECT_ID('tempdb..##tmpEventsOnCal') IS NOT NULL
					DROP TABLE ##tmpEventsOnCal;
				CREATE TABLE ##tmpEventsOnCal (eventID int, isSWL bit, [status] char(1), reason varchar(15), sourceCalendarID int, isPastEvent bit,
					startTime datetime, endTime datetime, timeZoneID int, timeZoneCode varchar(25), timeZoneAbbr varchar(4),
					displayStartTime datetime, displayEndTime datetime, displayTimeZoneID int, displayTimeZoneCode varchar(25),
					displayTimeZoneAbbr varchar(4), siteResourceID int, isAllDayEvent bit, hiddenFromCalendar bit,
					altRegistrationURL varchar(300), eventTitle varchar(200), eventSubTitle varchar(200), locationTitle varchar(200),
					categoryIDList varchar(max));

				<cfif NOT arguments.memberID>
					SET @groupPrintID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(arguments.catalogOrgCode).publicGroupPrintID#">;
				<cfelse>
					SET @groupPrintID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objMember.getMemberGroupPrintID(memberID=arguments.memberID)#">;
				</cfif>

				<cfif listLen(arguments.strFilters.subjects)>
					select top 1 @calendarID = calendarID from @evCategories;

					select @categoryIDList = COALESCE(@categoryIDList + ',','') + cast(categoryID as varchar(10))
					from @evCategories;
				<cfelse>
					DECLARE @tmpCalendarCategories TABLE (calendarID int, categoryID int);

					INSERT INTO @tmpCalendarCategories (calendarID, categoryID)
					select pe.calendarID, pe.categoryID
					from dbo.tblParticipants as p
					inner join dbo.tblParticipantEvents as pe on pe.participantID = p.participantID
					where p.orgCode = @catalogOrgCode;

					select top 1 @calendarID = calendarID from @tmpCalendarCategories;

					select @categoryIDList = COALESCE(@categoryIDList + ',', '') + cast(c.categoryID as varchar(10))
					from memberCentral.dbo.ev_categories as c
					inner join @tmpCalendarCategories as tmp on tmp.categoryID = c.categoryID
					where c.calendarID = @calendarID;
				</cfif>

				EXEC memberCentral.dbo.ev_getEventsOnCalendar @calendarID=@calendarID, @startDate=@nowDate, @endDate=null,
					@categoryIDList=@categoryIDList, @limitToVisibleCalendars=1, @groupPrintID=@groupPrintID, @includeSWL=0;

				INSERT INTO ##tmpSWPrograms (programID, programName, programSubTitle, programLocation, isRegistered, 
					dateStart, dateEnd, displayStartTime, displayEndTime, displayTimeZoneID, displayTimeZoneCode, 
					displayTimeZoneAbbr, featureImageSiteCode, featureImageOrgCode, featureImageID, 
					featureImageFileExtension, featureImageSizeID, isFeatured, isSaved, publisherOrgCode, ft, itemType, allowRegistrants, altRegistrationURL, [rank])
				SELECT tmp.eventID, tmp.eventTitle, tmp.eventSubTitle, tmp.locationTitle, 
					case when @memberID > 0 then memberCentral.dbo.fn_ev_isUserRegisteredForEvent(@memberID,tmp.eventID) else 0 end,
					tmp.startTime, tmp.endTime, tmp.displayStartTime, tmp.displayEndTime, tmp.displayTimeZoneID, 
					tmp.displayTimeZoneCode, tmp.displayTimeZoneAbbr, s.siteCode, o.orgCode, fiu.featureImageID, 
					fics.fileExtension, ficus.featureImageSizeID, CASE WHEN fp.featuredID IS NULL THEN 0 ELSE 1 END, 0, 
					@catalogOrgCode, 'EV', 'EV-' + cast(tmp.eventID as varchar(10)), 0, tmp.altRegistrationURL,
					<cfif len(local.searchterms)>swSearch.[rank]<cfelse>0</cfif>
				FROM ##tmpEventsOnCal as tmp
				<cfif len(local.searchterms)>
					INNER JOIN ##swFulltext as swSearch on swSearch.programType = 'EV' and swSearch.programID = tmp.eventID
				</cfif>
				INNER JOIN memberCentral.dbo.ev_calendars as c on c.calendarID = tmp.sourceCalendarID
				INNER JOIN memberCentral.dbo.sites AS s ON s.siteID = c.siteID
				INNER JOIN memberCentral.dbo.organizations AS o ON o.orgID = s.orgID
				LEFT OUTER JOIN dbo.tblFeaturedPrograms AS fp on fp.participantID = @participantID
					AND fp.eventID = tmp.eventID
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = c.calendarID
					AND ficu.referenceType = 'calendarEvent'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageUsages AS fiu 
					INNER JOIN memberCentral.dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
					ON fiu.featureImageConfigID = ficu.featureImageConfigID 
					AND fiu.referenceID = tmp.eventID
					AND fiu.referenceType = 'eventsList'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsagesAndSizes AS ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
					AND ficus.referenceType = 'viewEventsList'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigSizes AS fics on fics.featureImageSizeID = ficus.featureImageSizeID
				WHERE tmp.status = 'A'
				and isNull(tmp.hiddenFromCalendar,0) = 0;

				IF @savedKey is not null
					UPDATE tmp
					SET tmp.isSaved = 1
					FROM ##tmpSWPrograms as tmp
					INNER JOIN dbo.tblSavedPrograms AS sp ON sp.savedKey = @savedKey 
						AND sp.programType = 'ev' 
						AND sp.programID = tmp.programID;
			</cfif>
			
			<!--- SWL --->
			<cfif local.includeSWL>
				INSERT INTO ##tmpSWPrograms (programID, programName, programSubTitle, programDesc, enrollmentID, dateStart, dateEnd, 
					wddxTimeZones, publisherOrgCode, linkedSWODSeminarID, freeRateDisplay, featureImageSiteCode, featureImageOrgCode, 
					featureImageID, featureImageFileExtension, featureImageSizeID, isFeatured, isSaved, ft, itemType, allowRegistrants, altRegistrationURL, [rank])
				SELECT s.seminarID, s.seminarName, s.seminarSubTitle, s.seminarDesc, 
					case when @memberID > 0 then dbo.fn_getEnrollmentIDForSeminar(s.seminarID,@memberID) else 0 end,
					swl.dateStart, swl.dateEnd, swl.wddxTimeZones, p.orgcode, ISNULL(swod.seminarID,0), 
					s.freeRateDisplay, mcs.siteCode, o.orgCode, fiu.featureImageID, fics.fileExtension, ficus.featureImageSizeID, 
					CASE WHEN fp.featuredID IS NULL THEN 0 ELSE 1 END, 0, 'SWL',
					'SWL-' + cast(s.seminarID as varchar(10)), s.allowRegistrants, '',
					<cfif len(local.searchterms)>swSearch.[rank]<cfelse>0</cfif>
				FROM dbo.tblSeminars AS s
				INNER JOIN ##tmp_SWBrowse_SWL AS smc ON smc.seminarID = s.seminarID
				INNER JOIN dbo.tblSeminarsSWLive AS swl ON swl.seminarID = s.seminarID
				<cfif len(local.searchterms)>
					INNER JOIN ##swFulltext as swSearch on swSearch.programType = 'SWL' and swSearch.programID = s.seminarID
				</cfif>
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
				INNER JOIN memberCentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
				INNER JOIN memberCentral.dbo.organizations AS o ON o.orgID = mcs.orgID
				LEFT OUTER JOIN dbo.tblFeaturedPrograms AS fp on fp.participantID = @participantID
					AND fp.seminarID = smc.seminarID
				LEFT OUTER JOIN dbo.tblSeminarsSWOD AS swod 
					INNER JOIN ##tmp_SWBrowse_SWOD AS smcswod on smcswod.seminarID = swod.convertedFromSeminarID
					ON swod.convertedFromSeminarID = s.seminarID
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = mcs.siteID
					AND ficu.referenceType = 'swProgram'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageUsages AS fiu 
					INNER JOIN memberCentral.dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
					ON fiu.featureImageConfigID = ficu.featureImageConfigID 
					AND fiu.referenceID = s.seminarID
					AND fiu.referenceType = 'swlProgram'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsagesAndSizes AS ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
					AND ficus.referenceType = @swFtdImageSizeReferenceType
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigSizes AS fics on fics.featureImageSizeID = ficus.featureImageSizeID
				WHERE s.isDeleted = 0
				AND swl.dateStart > @nowDate
				AND @nowDate between s.dateCatalogStart AND s.dateCatalogEnd;

				IF @savedKey is not null
					UPDATE tmp
					SET tmp.isSaved = 1
					FROM ##tmpSWPrograms as tmp
					INNER JOIN dbo.tblSavedPrograms AS sp ON sp.savedKey = @savedKey 
						AND sp.programType = 'swl' 
						AND sp.programID = tmp.programID;
			</cfif>

			<!--- SWOD --->
			<cfif local.includeSWOD>
				INSERT INTO ##tmpSWPrograms (programID, programName, programSubTitle, programDesc, enrollmentID,  
					dateActivated, publisherOrgCode, freeRateDisplay, featureImageSiteCode, featureImageOrgCode, featureImageID, 
					featureImageFileExtension, featureImageSizeID, isFeatured, isSaved, ft, itemType, allowRegistrants, altRegistrationURL, [rank])
				SELECT s.seminarID, s.seminarName, s.seminarSubTitle, s.seminarDesc, 
					case when @memberID > 0 then dbo.fn_getEnrollmentIDForSeminar(s.seminarID,@memberID) else 0 end,
					sod.dateActivated, p.orgcode, s.freeRateDisplay, mcs.siteCode, o.orgCode, fiu.featureImageID, fics.fileExtension, 
					ficus.featureImageSizeID, CASE WHEN fp.featuredID IS NULL THEN 0 ELSE 1 END, 0,
					'SWOD', 'SWOD-' + cast(s.seminarID as varchar(10)), s.allowRegistrants, '', <cfif len(local.searchterms)>swSearch.[rank]<cfelse>0</cfif>
				FROM dbo.tblSeminars AS s
				INNER JOIN ##tmp_SWBrowse_SWOD AS smc on smc.seminarID = s.seminarID
				<cfif len(local.searchterms)>
					INNER JOIN ##swFulltext as swSearch on swSearch.programType = 'SWOD' and swSearch.programID = s.seminarID
				</cfif>
				INNER JOIN dbo.tblSeminarsSWOD AS sod on sod.seminarID = smc.seminarID
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = s.participantID
				INNER JOIN memberCentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
				INNER JOIN memberCentral.dbo.organizations AS o ON o.orgID = mcs.orgID
				LEFT OUTER JOIN dbo.tblFeaturedPrograms AS fp on fp.participantID = @participantID
					AND fp.seminarID = smc.seminarID
				LEFT OUTER JOIN dbo.tblSavedPrograms AS sp ON sp.savedKey = @savedKey 
					AND sp.programType = 'swod' 
					AND sp.programID = s.seminarID
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = mcs.siteID
					AND ficu.referenceType = 'swProgram'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageUsages AS fiu 
					INNER JOIN memberCentral.dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
					ON fiu.featureImageConfigID = ficu.featureImageConfigID 
					AND fiu.referenceID = s.seminarID
					AND fiu.referenceType = 'swodProgram'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsagesAndSizes AS ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
					AND ficus.referenceType = @swFtdImageSizeReferenceType
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigSizes AS fics on fics.featureImageSizeID = ficus.featureImageSizeID;

				IF @savedKey is not null
					UPDATE tmp
					SET tmp.isSaved = 1
					FROM ##tmpSWPrograms as tmp
					INNER JOIN dbo.tblSavedPrograms AS sp ON sp.savedKey = @savedKey 
						AND sp.programType = 'swod' 
						AND sp.programID = tmp.programID;
			</cfif>

			<!--- SWB --->
			<cfif local.includeSWB>
				INSERT INTO ##tmpSWPrograms (programID, programName, programSubTitle, programDesc, enrollmentID,  
					dateActivated, publisherOrgCode, freeRateDisplay, isSWODBundle, featureImageSiteCode, 
					featureImageOrgCode, featureImageID, featureImageFileExtension, featureImageSizeID, 
					isFeatured, isSaved, ft, itemType, allowRegistrants, altRegistrationURL, [rank])
				SELECT b.bundleID, b.bundleName, b.bundleSubTitle, b.bundleDesc, 
					case when @memberID > 0 then dbo.fn_getBundleOrderIDForBundle(b.bundleID,@memberID) else 0 end,					
					b.dateActivated, p.orgcode, b.freeRateDisplay, b.isSWOD, mcs.siteCode, o.orgCode, fiu.featureImageID, fics.fileExtension, 
					ficus.featureImageSizeID, CASE WHEN fp.featuredID IS NULL THEN 0 ELSE 1 END, 0,
					'SWB', 'SWB-' + cast(b.bundleID as varchar(10)), 0, '', <cfif len(local.searchterms)>swSearch.[rank]<cfelse>0</cfif>
				FROM dbo.tblBundles as b
				INNER JOIN ##tmp_SWBrowse_SWB AS bmc on bmc.bundleID = b.bundleID
				<cfif len(local.searchterms)>
					INNER JOIN ##swFulltext as swSearch on swSearch.programType = 'SWB' and swSearch.programID = b.bundleID
				</cfif>
				INNER JOIN dbo.tblParticipants AS p ON p.participantID = b.participantID
				INNER JOIN memberCentral.dbo.sites AS mcs ON mcs.siteCode = p.orgCode
				INNER JOIN memberCentral.dbo.organizations AS o ON o.orgID = mcs.orgID
				LEFT OUTER JOIN dbo.tblFeaturedPrograms AS fp on fp.participantID = @participantID
					AND fp.bundleID = bmc.bundleID
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsages AS ficu ON ficu.referenceID = mcs.siteID
					AND ficu.referenceType = 'swProgram'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageUsages AS fiu 
					INNER JOIN memberCentral.dbo.cms_featuredImages AS fi ON fi.featureImageID = fiu.featureImageID
					ON fiu.featureImageConfigID = ficu.featureImageConfigID 
					AND fiu.referenceID = b.bundleID
					AND fiu.referenceType = 'swbProgram'
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigUsagesAndSizes AS ficus on ficus.featureImageConfigUsageID = ficu.featureImageConfigUsageID
					AND ficus.referenceType = @swFtdImageSizeReferenceType
				LEFT OUTER JOIN memberCentral.dbo.cms_featuredImageConfigSizes AS fics on fics.featureImageSizeID = ficus.featureImageSizeID
				WHERE b.status = 'A'
				AND @nowDate BETWEEN b.dateCatalogStart AND b.dateCatalogEnd
				<cfif local.includeSWL AND NOT local.includeSWOD>
					AND b.isSWOD = 0
				<cfelseif NOT local.includeSWL AND local.includeSWOD>
					AND b.isSWOD = 1
				</cfif>
				AND EXISTS (SELECT 1 FROM dbo.tblBundledItems as bi 
								INNER JOIN dbo.tblSeminars AS s ON bi.seminarID = s.seminarID and s.isPublished = 1 AND s.isDeleted = 0 
								WHERE bi.bundleID = b.bundleID);

				-- search terms inside included programs
				<cfif len(local.searchterms)>
					UPDATE swb
					SET swb.rank = tmp.[rank]
					FROM ##tmpSWPrograms as swb
					CROSS APPLY (
						SELECT MAX(swSearch.[rank]) AS [rank]
						FROM dbo.tblBundledItems AS bi
						INNER JOIN dbo.tblSeminars AS s ON s.seminarID = bi.seminarID AND s.isPublished = 1 AND s.isDeleted = 0
						INNER JOIN ##swFulltext AS swSearch ON swSearch.programType IN ('SWL','SWOD') AND swSearch.programID = bi.seminarID
						WHERE bi.bundleID = swb.programID
					) tmp
					WHERE swb.ft = 'SWB'
					AND swb.[rank] IS NULL;

					DELETE FROM ##tmpSWPrograms
					WHERE ft = 'SWB'
					AND [rank] IS NULL;
				</cfif>

				IF @savedKey is not null
					UPDATE tmp
					SET tmp.isSaved = 1
					FROM ##tmpSWPrograms as tmp
					INNER JOIN dbo.tblSavedPrograms AS sp ON sp.savedKey = @savedKey 
						AND sp.programType = 'swb' 
						AND sp.programID = tmp.programID;

				-- swl bundles dateStart
				UPDATE swb
				SET swb.dateStart = tmp.dateStart, swb.dateEnd = tmp.dateEnd
				FROM ##tmpSWPrograms as swb
				CROSS APPLY (
					SELECT MIN(swl.dateStart) AS dateStart, MAX(swl.dateEnd) AS dateEnd
					FROM dbo.tblBundledItems AS bi
					INNER JOIN dbo.tblSeminars AS s ON s.seminarID = bi.seminarID AND s.isPublished = 1 AND s.isDeleted = 0
					INNER JOIN dbo.tblSeminarsSWLive AS swl on swl.seminarID = bi.seminarID
					WHERE bi.bundleID = swb.programID
				) tmp
				WHERE swb.ft = 'SWB'
				AND swb.isSWODBundle = 0;
			</cfif>

			-- update isRegistered
			UPDATE ##tmpSWPrograms
			SET isRegistered = CASE WHEN ft IN ('SWL','SWOD') AND enrollmentID > 0 THEN 1 ELSE ISNULL(isRegistered,0) END;

			-- filter programs
			<cfswitch expression="#arguments.mode#">
				<cfcase value="browse">
					INSERT INTO ##tmpFilteredSWPrograms (programID, ft)
					SELECT DISTINCT tmp.programID, tmp.ft
					FROM ##tmpSWPrograms as tmp
					<cfif listLen(arguments.strFilters.creditAuthorityList)>
						INNER JOIN dbo.tblSeminarsAndCredit as sac on tmp.ft in ('swl','swod') and sac.seminarID = tmp.programID
						INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.CSALinkID = sac.CSALinkID
						INNER JOIN dbo.tblCreditAuthorities as ca on csa.authorityID = ca.authorityID 
							AND ca.authorityID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.strFilters.creditAuthorityList#">)
						INNER JOIN dbo.tblCreditStatuses as cstat on sac.statusID = cstat.statusID AND cstat.status in ('Approved','Pending','Self-Submitting')
						LEFT OUTER JOIN dbo.tblSeminarsSWLive as sswl on sswl.seminarID = sac.seminarID
						LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = sac.seminarID
						CROSS APPLY sac.wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') as CRDA(credittype)
						CROSS APPLY ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') as CRDT(credittype)
					</cfif>
					<cfif listLen(arguments.strFilters.subjects)>
						LEFT OUTER JOIN dbo.tblSeminarsAndCategories as scat 
								INNER JOIN @swSubjectAreas as sc on sc.categoryID = scat.categoryID
							on tmp.ft in ('swl','swod') and scat.seminarID = tmp.programID
					</cfif>
					<cfif listLen(arguments.strFilters.authorIDList)>
						INNER JOIN @swProgramSpeakers as aut on aut.programID = tmp.programID
							and aut.ft = tmp.ft
					</cfif>
					<cfif listLen(arguments.strFilters.participantIDList)>
						INNER JOIN @swPublishers as swp on swp.participantOrgCode = tmp.publisherOrgCode
					</cfif>
					WHERE tmp.ft <> 'swb'
					<cfif arguments.strFilters.isRegistered>
						AND tmp.isRegistered = 1
					</cfif>
					<cfif arguments.strFilters.isSaved>
						AND tmp.isSaved = 1
					</cfif>
					<cfif listLen(arguments.strFilters.creditAuthorityList)>
						AND (
							sswl.liveid is not null
							OR
							sswod.ondemandID is not null and GETDATE() between sac.creditOfferedStartDate and sac.creditOfferedEndDate
							)
						AND CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') > 0
						AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)')
						<cfif len(arguments.strFilters.creditTypes)>
							AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') IN (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="true" value="#arguments.strFilters.creditTypes#">)
						</cfif>
					</cfif>
					<cfif listLen(arguments.strFilters.subjects)>
						AND (sc.categoryID is not null OR tmp.ft = 'ev')
					</cfif>;

					-- bundle programs filter
					<cfif local.includeSWB>
						INSERT INTO ##tmpFilteredSWPrograms (programID, ft)
						SELECT DISTINCT tmp.programID, tmp.ft
						FROM ##tmpSWPrograms as tmp
						INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = tmp.programID and tmp.ft = 'swb'
						<cfif listLen(arguments.strFilters.creditAuthorityList)>
							INNER JOIN dbo.tblSeminarsAndCredit as sac on sac.seminarID = bi.seminarID
							INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.CSALinkID = sac.CSALinkID
							INNER JOIN dbo.tblCreditAuthorities as ca on csa.authorityID = ca.authorityID 
								AND ca.authorityID IN (<cfqueryparam cfsqltype="CF_SQL_INTEGER" list="true" value="#arguments.strFilters.creditAuthorityList#">)
							INNER JOIN dbo.tblCreditStatuses as cstat on sac.statusID = cstat.statusID AND cstat.status in ('Approved','Pending','Self-Submitting')
							LEFT OUTER JOIN dbo.tblSeminarsSWLive as sswl on sswl.seminarID = sac.seminarID
							LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = sac.seminarID
							CROSS APPLY sac.wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') as CRDA(credittype)
							CROSS APPLY ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') as CRDT(credittype)
						</cfif>
						<cfif listLen(arguments.strFilters.subjects)>
							INNER JOIN dbo.tblSeminarsAndCategories as scat on scat.seminarID = bi.seminarID
							INNER JOIN @swSubjectAreas as sc on sc.categoryID = scat.categoryID
						</cfif>
						<cfif listLen(arguments.strFilters.authorIDList)>
							INNER JOIN @swProgramSpeakers as aut on aut.programID = tmp.programID
								and aut.ft = tmp.ft
						</cfif>
						<cfif listLen(arguments.strFilters.participantIDList)>
							INNER JOIN @swPublishers as swp on swp.participantOrgCode = tmp.publisherOrgCode
						</cfif>
						WHERE 1 = 1
						<cfif arguments.strFilters.isRegistered>
							AND tmp.isRegistered = 1
						</cfif>
						<cfif arguments.strFilters.isSaved>
							AND tmp.isSaved = 1
						</cfif>
						<cfif listLen(arguments.strFilters.creditAuthorityList)>
							AND (
								sswl.liveid is not null
								OR
								sswod.ondemandID is not null and GETDATE() between sac.creditOfferedStartDate and sac.creditOfferedEndDate
								)
							AND CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') > 0
							AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)')
							<cfif len(arguments.strFilters.creditTypes)>
								AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') IN (<cfqueryparam cfsqltype="CF_SQL_VARCHAR" list="true" value="#arguments.strFilters.creditTypes#">)
							</cfif>
						</cfif>;
					</cfif>

					SELECT @totalCount = COUNT(programID) FROM ##tmpFilteredSWPrograms;
				</cfcase>
				<cfcase value="featured">
					INSERT INTO ##tmpFilteredSWPrograms (programID, ft)
					SELECT programID, ft
					FROM ##tmpSWPrograms
					WHERE isFeatured = 1;

					SET @totalCount = @@ROWCOUNT;

					IF @totalCount < 4 BEGIN
						INSERT INTO ##tmpFilteredSWPrograms (programID, ft)
						SELECT programID, ft
						FROM ##tmpSWPrograms
							EXCEPT
						SELECT programID, ft
						FROM ##tmpFilteredSWPrograms;

						SELECT @totalCount = COUNT(programID) FROM ##tmpFilteredSWPrograms;

						SELECT @posStart = 1, @posStartPlusCount = CASE WHEN @totalCount > 4 THEN 5 ELSE @totalCount + 1 END;
					END
					ELSE
						SELECT @posStart = 1, @posStartPlusCount = @totalCount + 1;
				</cfcase>
				<cfcase value="myFeatured">
					INSERT INTO ##tmpFilteredSWPrograms (programID, ft)
					SELECT programID, ft
					FROM ##tmpSWPrograms;

					SET @totalCount = @@ROWCOUNT;

					SELECT @posStart = 1, @posStartPlusCount = CASE WHEN @totalCount > 3 THEN 4 ELSE @totalCount + 1 END;
				</cfcase>
				<cfcase value="mergecode">
					INSERT INTO ##tmpFilteredSWPrograms (programID, ft)
					SELECT DISTINCT tmp.programID, tmp.ft
					FROM ##tmpSWPrograms as tmp
					WHERE tmp.ft <> 'swb';

					-- bundle programs filter
					<cfif local.includeSWL OR local.includeSWOD>
						INSERT INTO ##tmpFilteredSWPrograms (programID, ft)
						SELECT DISTINCT tmp.programID, tmp.ft
						FROM ##tmpSWPrograms as tmp
						INNER JOIN dbo.tblBundledItems as bi on bi.bundleID = tmp.programID and tmp.ft = 'swb'
						WHERE 1 = 1;
					</cfif>

					SELECT @totalCount = COUNT(programID) FROM ##tmpFilteredSWPrograms;
				</cfcase>
			</cfswitch>

			-- when ordering by date, we need to to determine what "date" to use for non EV/SWL entries so those show up first.
			declare @maxDateStartPlusTwo datetime;
			select @maxDateStartPlusTwo = MAX(dateStart) from ##tmpSWPrograms;
			IF @maxDateStartPlusTwo is null
				SET @maxDateStartPlusTwo = GETDATE();
			SET @maxDateStartPlusTwo = dateadd(day,2,@maxDateStartPlusTwo);

			-- order by
			;WITH swProgramsCTE AS (
				SELECT tmp.programID, tmp.ft, ROW_NUMBER() OVER (ORDER BY #preserveSingleQuotes(local.orderBy)#) as rowNum
				FROM ##tmpSWPrograms AS tmp
				INNER JOIN ##tmpFilteredSWPrograms AS fSW ON fSW.programID = tmp.programID AND fSW.ft = tmp.ft
			)
			UPDATE tmp
			SET tmp.rowNum = cte.rowNum
			FROM ##tmpSWPrograms as tmp
			INNER JOIN swProgramsCTE as cte on cte.programID = tmp.programID
				AND cte.ft = tmp.ft;
			
			SELECT programID, programName, programSubTitle, programDesc, programLocation, enrollmentID, isRegistered, 
				dateStart, dateEnd, displayStartTime, displayEndTime, displayTimeZoneID, displayTimeZoneCode, displayTimeZoneAbbr, 
				wddxTimeZones, publisherOrgCode, linkedSWODSeminarID, freeRateDisplay, featureImageSiteCode, featureImageOrgCode, isSWODBundle,
				featureImageID, featureImageFileExtension, featureImageSizeID, isFeatured, isSaved, ft, itemType, @totalCount as totalCount, allowRegistrants, altRegistrationURL
			FROM ##tmpSWPrograms
			WHERE rowNum >= @posStart
			AND rowNum < @posStartPlusCount
			ORDER BY rowNum;

			IF OBJECT_ID('tempdb..##tmpSWPrograms') IS NOT NULL 
				DROP TABLE ##tmpSWPrograms;
			IF OBJECT_ID('tempdb..##tmpFilteredSWPrograms') IS NOT NULL 
				DROP TABLE ##tmpFilteredSWPrograms;
			<cfif len(local.searchterms)>
				IF OBJECT_ID('tempdb..##swFulltext') IS NOT NULL 
					DROP TABLE ##swFulltext;
			</cfif>
			<cfif local.includeConf>
				IF OBJECT_ID('tempdb..##tmpEventsOnCal') IS NOT NULL
					DROP TABLE ##tmpEventsOnCal;
			</cfif>
			<cfif local.includeSWL>
				IF OBJECT_ID('tempdb..##tmp_SWBrowse_SWL') IS NOT NULL 
					DROP TABLE ##tmp_SWBrowse_SWL;
			</cfif>
			<cfif local.includeSWL OR local.includeSWOD>
				IF OBJECT_ID('tempdb..##tmp_SWBrowse_SWOD') IS NOT NULL 
					DROP TABLE ##tmp_SWBrowse_SWOD;
			</cfif>
			<cfif local.includeSWB>
				IF OBJECT_ID('tempdb..##tmp_SWBrowse_SWB') IS NOT NULL 
					DROP TABLE ##tmp_SWBrowse_SWB;
			</cfif>

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfif local.qryPrograms.recordCount>
			<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
			<cfset local.objTSTZ = CreateObject("component","model.system.platform.tsTimeZone")>

			<cfif arguments.mode eq 'browse'>
				<cfset local.strReturn.strPagination.totalCount = local.qryPrograms.totalCount>
				<cfset local.strReturn.strPagination.numTotalPages = Ceiling(local.qryPrograms.totalCount / local.strReturn.strPagination.count)>
				<cfset local.strReturn.strPagination.numCurrentPage = int((int(arguments.strFilters.startPos) + local.strReturn.strPagination.count - 1) / local.strReturn.strPagination.count)>

				<cfif local.strReturn.strPagination.numCurrentPage gt 1 and (Ceiling((local.strReturn.strPagination.numCurrentPage + (local.strReturn.strPagination.maxPage / 2))) gt local.strReturn.strPagination.maxPage)>
					<cfset local.strReturn.strPagination.endPage = Ceiling(local.strReturn.strPagination.numCurrentPage + (local.strReturn.strPagination.maxPage / 2))>
					<cfset local.strReturn.strPagination.startPage = local.strReturn.strPagination.endPage - (local.strReturn.strPagination.maxPage - 1)>
					<cfif local.strReturn.strPagination.endPage gte local.strReturn.strPagination.numTotalPages>
						<cfset local.strReturn.strPagination.endPage = local.strReturn.strPagination.numTotalPages>
						<cfif local.strReturn.strPagination.endPage lte local.strReturn.strPagination.maxPage>
							<cfset local.strReturn.strPagination.startPage = 1>
						<cfelse>
							<cfset local.strReturn.strPagination.startPage = local.strReturn.strPagination.endPage - (local.strReturn.strPagination.maxPage - 1)>
						</cfif>
					</cfif>
				<cfelseif local.strReturn.strPagination.numCurrentPage gte 1 and local.strReturn.strPagination.numCurrentPage lte local.strReturn.strPagination.maxPage and local.strReturn.strPagination.maxPage lte local.strReturn.strPagination.numTotalPages>
					<cfset local.strReturn.strPagination.endPage = local.strReturn.strPagination.maxPage>
				<cfelseif local.strReturn.strPagination.numTotalPages lte local.strReturn.strPagination.maxPage>
					<cfset local.strReturn.strPagination.endPage = local.strReturn.strPagination.numTotalPages>
				</cfif>

				<cfif arguments.depomemberdataID gt 0>
					<cfset local.objSWCommon = CreateObject("component","model.seminarweb.SWCommon")>
					<cfset local.memberID = local.objSWCommon.getMemberIDByDepoMemberDataID(siteCode=arguments.catalogOrgCode, depoMemberDataID=arguments.depomemberdataID)>
					<cfset local.strEnrollments = local.objSWCommon.getEnrollmentHistory(MCMemberID=arguments.memberID, catalogOrgCode=arguments.catalogOrgCode)>
				</cfif>
			</cfif>
			
			<cfset local.defaultFeaturedImagePathsStr = getDefaultProgramFeaturedImagePaths(strAssociation=arguments.strAssociation, mode=arguments.mode)>
			<cfset local.defaultSWLFeaturedImagePath = local.defaultFeaturedImagePathsStr.defaultSWLFeaturedImagePath>
			<cfset local.defaultSWODFeaturedImagePath = local.defaultFeaturedImagePathsStr.defaultSWODFeaturedImagePath>
			<cfset local.defaultSWBFeaturedImagePath = local.defaultFeaturedImagePathsStr.defaultSWBFeaturedImagePath>
			<cfset local.defaultEVFeaturedImagePath = local.defaultFeaturedImagePathsStr.defaultEVFeaturedImagePath>

			<cfset local.qrySeminars = QueryFilter(local.qryPrograms, 
														function(thisRow) {  
															return listFindNoCase('SWL,SWOD',arguments.thisRow.ft);
														})>
			<cfset local.seminarIDList = "0#valueList(local.qrySeminars.programID)#">

			<cfset local.qryEvents = QueryFilter(local.qryPrograms, 
													function(thisRow) {  
														return listFindNoCase('EV',arguments.thisRow.ft);
													})>
			<cfset local.eventIDList = "0#valueList(local.qryEvents.programID)#">

			<cfset local.qryBundles = QueryFilter(local.qryPrograms, 
													function(thisRow) {  
														return listFindNoCase('SWB',arguments.thisRow.ft);
													})>
			<cfset local.bundleIDList = "0#valueList(local.qryBundles.programID)#">

			<cfif listFindNoCase("browse,mergecode",arguments.mode)>
				<cfstoredproc datasource="#application.dsn.tlasites_seminarweb.dsn#" procedure="sw_getProgramRatesForBrowse">
					<cfprocparam type="in" cfsqltype="CF_SQL_VARCHAR" value="#arguments.catalogOrgCode#">
					<cfprocparam type="in" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.seminarIDList#">
					<cfprocparam type="in" cfsqltype="CF_SQL_LONGVARCHAR" value="#local.bundleIDList#">
					<cfprocresult name="local.qryProgramRates">
				</cfstoredproc>
			</cfif>

			<cfif local.qryEvents.recordCount>
				<cfif arguments.mode eq 'browse'>
					<cfquery name="local.qryEventRates" datasource="#application.dsn.memberCentral.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @siteID int, @eventIDList varchar(max);
						SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.siteID#">;
						SET @eventIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.eventIDList#">;

						select e.eventID, r.rateID, r.rateName, r.rate, r.freeRateDisplay, 
							ROW_NUMBER() OVER (PARTITION BY e.eventID ORDER BY rging.rateGroupingOrder, r.rateOrder) as rowNum
						from dbo.fn_intListToTableInline(@eventIDList,',') as tmp
						inner join dbo.ev_events as e on e.siteID = @siteID and e.eventID = tmp.listitem
						inner join dbo.ev_registration as er on er.eventID = e.eventID
							and er.siteID = @siteID
							and er.status = 'A'
						inner join dbo.ev_rates as r on r.registrationID = er.registrationID
						inner join dbo.cms_siteResources as sr on sr.siteID = @siteID and sr.siteResourceID = r.siteResourceID and sr.siteResourceStatusID = 1
						inner join dbo.ev_ratesAvailable ra on ra.rateID = r.rateid
						inner join dbo.ev_priceSchedule ps on ps.scheduleID = ra.scheduleID
						left outer join dbo.ev_rateGrouping as rging on rging.rateGroupingID = r.rateGroupingID
						where getDate() between ps.startDate and ps.endDate
						and r.rate >= 0
						and r.isHidden = 0
						and r.parentRateID is null;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>

					<cfset local.evRegCart = CreateObject("component","model.events.eventRegV2").evRegCartToQuery()>
					<cfset local.mc_siteinfo = application.objSiteInfo.getSiteInfo(arguments.catalogOrgCode)>
					<cfset local.displayedCurrencyType = "">
					<cfif local.mc_siteinfo.showCurrencyType is 1>
						<cfset local.displayedCurrencyType = " #local.mc_siteinfo.defaultCurrencyType#">
					</cfif>
				</cfif>

				<cfquery name="local.qryEventCalendars" datasource="#application.dsn.memberCentral.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @siteID int, @eventIDList varchar(max);
					SET @siteID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#application.objSiteInfo.getSiteInfo(arguments.catalogOrgCode).siteID#">;
					SET @eventIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.eventIDList#">;

					select e.eventID, c.applicationInstanceID as calendarApplicationInstanceID
					from dbo.ev_events as e
					inner join dbo.fn_intListToTableInline(@eventIDList,',') as tmp on tmp.listitem = e.eventID
					inner join dbo.ev_calendarEvents as ce on ce.sourceEventID = e.eventID and ce.calendarID = ce.sourceCalendarID
					inner join dbo.ev_calendars as c on c.siteID = @siteID and c.calendarID = ce.calendarID
					where e.siteID = @siteID;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>
			</cfif>

			<cfif listFindNoCase("browse,mergecode",arguments.mode)>
				<cfif local.qryBundles.recordCount>
					<cfquery name="local.qryBundleItems" datasource="#application.dsn.tlasites_seminarweb.dsn#">
						SET NOCOUNT ON;
						SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

						DECLARE @bundleIDList varchar(max), @memberID int;
						SET @bundleIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.bundleIDList#">;
						SET @memberID = <cfqueryparam cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">;

						SELECT b.bundleID, bi.itemOrder, bi.format,	bi.contentID, bi.contentName, bi.contentSubTitle, bi.isRegistered, bi.dateStart
						FROM memberCentral.dbo.fn_intListToTableInline(@bundleIDList,',') as tmp
						INNER JOIN dbo.tblBundles as b on b.bundleID = tmp.listitem
						CROSS APPLY dbo.swb_getBundledItemsForCatalog(b.bundleID,@memberID) as bi;

						SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
					</cfquery>
				</cfif>

				<cfquery name="local.qryProgramLearningObjectives" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @seminarIDList varchar(max), @bundleIDList varchar(max);
					SET @seminarIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.seminarIDList#">;
					SET @bundleIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.bundleIDList#">;

					SELECT lo.seminarID as programID, lo.objectiveID, lo.objective, lo.objectiveOrder,
						1 as isSeminar, 0 as isTitle, 0 as isBundle
					FROM memberCentral.dbo.fn_intListToTableInline(@seminarIDList,',') as tmp
					INNER JOIN dbo.tblLearningObjectives AS lo on lo.seminarID = tmp.listitem
						UNION
					SELECT lo.bundleID as programID, lo.objectiveID, lo.objective, lo.objectiveOrder,
						0 as isSeminar, 0 as isTitle, 1 as isBundle
					FROM memberCentral.dbo.fn_intListToTableInline(@bundleIDList,',') as tmp
					INNER JOIN dbo.tblLearningObjectives AS lo on lo.bundleID = tmp.listitem

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfquery name="local.qryProgramCredits" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @seminarIDList varchar(max);
					SET @seminarIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.seminarIDList#">;

					SELECT sac.seminarID as programID, ca.code as authorityCode, ca.authorityName,
						CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') AS creditValueAwarded,
						CRDT.credittype.value('(var[@name="displayname"]/string)[1]','varchar(200)') AS creditType
					FROM dbo.tblSeminarsAndCredit AS sac 
					INNER JOIN membercentral.dbo.fn_IntListToTable(@seminarIDList,',') as s on s.listitem = sac.seminarID
					INNER JOIN dbo.tblCreditSponsorsAndAuthorities as csa on csa.CSALinkID = sac.CSALinkID
					INNER JOIN dbo.tblCreditAuthorities AS ca ON csa.authorityID = ca.authorityID 
					INNER JOIN dbo.tblCreditStatuses AS cstat ON sac.statusID = cstat.statusID 
					LEFT OUTER JOIN dbo.tblSeminarsSWLive as sswl on sswl.seminarID = s.listitem
					LEFT OUTER JOIN dbo.tblSeminarsSWOD as sswod on sswod.seminarID = s.listitem
					CROSS APPLY sac.wddxCreditsAvailable.nodes('/wddxPacket/data/array/struct') AS CRDA(credittype)
					CROSS APPLY ca.wddxCreditTypes.nodes('/wddxPacket/data/array/struct') AS CRDT(credittype)
					WHERE cstat.status in ('Approved','Pending','Self-Submitting')
					AND (
						sswl.liveid is not null
						OR
						sswod.ondemandID is not null and getdate() between sac.creditOfferedStartDate and sac.creditOfferedEndDate
						)
					AND CRDA.credittype.value('(var[@name="value"]/string)[1]','numeric(9,2)') > 0
					AND CRDA.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)') = CRDT.credittype.value('(var[@name="fieldname"]/string)[1]','varchar(200)');

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfquery name="local.qryProgramInBundles" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @seminarIDList varchar(max);
					SET @seminarIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.seminarIDList#">;

					SELECT b.bundleName, s.seminarID AS contentID, 
						CASE 
						WHEN swl.liveID is not null then 'SWL'
						WHEN sod.onDemandID is not null then 'SWOD'
						END as 'contentType'
					FROM dbo.tblBundledItems AS bi 
					INNER JOIN dbo.tblSeminars AS s ON bi.seminarID = s.seminarID AND s.isDeleted = 0
					INNER JOIN membercentral.dbo.fn_IntListToTable(@seminarIDList,',') as tmp on tmp.listitem = s.seminarID
					INNER JOIN dbo.tblBundles as b on b.bundleID = bi.bundleID
					LEFT OUTER JOIN dbo.tblSeminarsSWLive AS swl ON s.seminarID = swl.seminarID
					LEFT OUTER JOIN dbo.tblSeminarsSWOD AS sod ON s.seminarID = sod.seminarID;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfquery name="local.qryProgramVideoPreviews" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					SET NOCOUNT ON;
					SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

					DECLARE @seminarIDList varchar(max);
					SET @seminarIDList = <cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#local.seminarIDList#">;

					SELECT f.fileID, f.fileName, f.fileTitle, sat.seminarID as programID, p.participantID, p.orgcode as participantOrgCode,
						cast(f.fileID as varchar(10)) + '_s_' + cast(sat.seminarID as varchar(10)) + '_preview.mp4' as videoPreviewFileName
					FROM dbo.tblVideoPreviews as vp
					INNER JOIN dbo.tblFiles as f on f.fileID = vp.baseFileID
					INNER JOIN dbo.tblFilesTypes as ft on ft.fileTypeID = f.fileTypeID
					INNER JOIN dbo.tblTitlesAndFiles as taf on taf.fileID = f.fileID
					INNER JOIN dbo.tblTitles as t on t.titleid = taf.titleID
					INNER JOIN dbo.tblSeminarsAndTitles as sat on sat.titleID = taf.titleID and sat.seminarID = vp.seminarID
					INNER JOIN membercentral.dbo.fn_IntListToTable(@seminarIDList,',') as tmp on tmp.listitem = sat.seminarID
					INNER JOIN dbo.tblParticipants as p on p.participantID = t.participantID
					WHERE vp.isOnline = 1
					AND f.isDeleted = 0
					AND t.isDeleted = 0;

					SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
				</cfquery>

				<cfif local.qryProgramVideoPreviews.recordCount>
					<cfset local.objSWFiles = CreateObject("component","model.seminarweb.SWFiles")>
				</cfif>
			</cfif>

			<cfloop query="local.qryPrograms">
				<cfset local.programFtdThumbImgFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.qryPrograms.featureImageOrgCode)#/#LCASE(local.qryPrograms.featureImageSiteCode)#/featuredimages/thumbnails/">
				<cfset local.programFtdThumbImgRootPath = "/userassets/#LCASE(local.qryPrograms.featureImageOrgCode)#/#LCASE(local.qryPrograms.featureImageSiteCode)#/featuredimages/thumbnails/">

				<cfswitch expression="#local.qryPrograms.ft#">
					<cfcase value="SWL">
						<cfset local.tmpStr = { "ft":local.qryPrograms.ft, "programID":local.qryPrograms.programID, "programTitle":local.qryPrograms.programName,
												"programSubTitle":local.qryPrograms.programSubTitle, "dspStartDate":"", "dspEndDate":"", "dspTZ":"", "dspTZStr":"", 
												"isRegistered":local.qryPrograms.isRegistered, "linkedSWODSeminarID":local.qryPrograms.linkedSWODSeminarID, 
												"freeRateDisplay":local.qryPrograms.freeRateDisplay, "isFeatured":local.qryPrograms.isFeatured, 
												"arrLearningObjectives":[], "arrRates":[], "dspCredits":"", "inCart":0, "attended":0, "uptoCreditsCount":0, 
												"featuredImagePath":"", "isSaved":local.qryPrograms.isSaved, "programBrand":arguments.qrySWP.brandSWLTab, 
												"incBundlesList":[],"allowRegistrants":local.qryPrograms.allowRegistrants
											}>

						<cfset local.parsedTime = local.objSWL.parseTimesFromWDDX(seminarWDDXTimeZones=local.qryPrograms.wddxTimeZones, orgWDDXTimeZones=arguments.qrySWP.wddxTimeZones, ifErrStartTime=local.qryPrograms.dateStart, ifErrEndTime=local.qryPrograms.dateEnd)>
						<cfset local.qryTZ = local.objTSTZ.getTimeZones()>
						<cfquery name="local.qryTZ_selected" dbtype="query">
							select timeZoneAbbr from [local].qryTZ where timeZone = '#local.parsedTime.TimeZone#'
						</cfquery>

						<cfset local.tmpStr.dspStartDate = local.parsedTime.StartDate>
						<cfset local.tmpStr.dspEndDate = local.parsedTime.EndDate>
						<cfset local.tmpStr.dspTZStr = local.parsedTime.TimeZoneCompare>
						<cfset local.tmpStr.dspTZ = local.qryTZ_selected.timeZoneAbbr>

						<cfif arguments.mode eq 'browse'>
							<cfif local.qryProgramLearningObjectives.recordCount>
								<cfquery name="local.tmpStr.arrLearningObjectives" dbtype="query" returntype="array">
									select objective
									from [local].qryProgramLearningObjectives
									where programID = #int(val(local.qryPrograms.programID))#
									and isSeminar = 1
									order by objectiveOrder
								</cfquery>
							</cfif>

							<cfif local.qryProgramRates.recordCount>
								<cfquery name="local.tmpStr.arrRates" dbtype="query" returntype="array">
									select price, description
									from [local].qryProgramRates
									where programID = #int(val(local.qryPrograms.programID))#
									and isSeminar = 1
									order by rowNum
								</cfquery>
							</cfif>

							<cfif local.qryProgramCredits.recordCount>
								<cfquery name="local.qryThisProgramCredits" dbtype="query">
									select authorityCode, creditType, max(creditValueAwarded) as creditValueAwarded
									from local.qryProgramCredits
									where programID = #int(val(local.qryPrograms.programID))#
									group by authorityCode, creditType
									order by authorityCode, creditType
								</cfquery>
								
								<cfif local.qryThisProgramCredits.recordCount>
									<cfquery name="local.qryUptoCreditCounts" dbtype="query">
										select authorityCode, sum(creditValueAwarded) as uptoCredits
										from local.qryThisProgramCredits
										group by authorityCode
										order by uptoCredits desc
									</cfquery>
									<cfset local.tmpStr.uptoCreditsCount = INT(val(local.qryUptoCreditCounts.uptoCredits))>
								
									<cfif local.qryUptoCreditCounts.recordCount is 1>
										<cfset local.tmpStr.dspCredits = "<span class='offeredCred'>Credits Offered: </span>">
										<cfloop query="local.qryThisProgramCredits">
											<cfset local.tmpStr.dspCredits = local.tmpStr.dspCredits & "#local.qryThisProgramCredits.creditValueAwarded# #local.qryThisProgramCredits.creditType#, ">
										</cfloop>
										<cfset local.tmpStr.dspCredits = mid(local.tmpStr.dspCredits, 1, len(local.tmpStr.dspCredits) - 2)>
									<cfelse>
										<cfset local.tmpStr.dspCredits = "Credit approved by <a href='{{creditsViewLink}}' class='{{classList}}'>#local.qryUptoCreditCounts.recordCount#</a> authorities">
									</cfif>
								</cfif>
							</cfif>

							<cfif local.qryProgramInBundles.recordCount>
								<cfquery name="local.qryIncBundles" dbtype="query">
									select distinct bundleName
									from [local].qryProgramInBundles
									where contentID = #int(val(local.qryPrograms.programID))#
									and contentType = 'SWL'
									order by bundleName
								</cfquery>
								<cfset local.tmpStr.incBundlesList = jsStringFormat(valueList(local.qryIncBundles.bundleName,'<br />'))>
							</cfif>

							<cfif arguments.swRegCart.recordCount AND arguments.memberID gt 0>
								<cfquery name="local.qryThisProgramInCart" dbtype="query">
									select memberid
									from arguments.swRegCart
									where memberid = #int(val(arguments.memberID))#
									and item = 'SWL-#int(val(local.qryPrograms.programID))#'
								</cfquery>

								<cfquery name="local.qryAllChildProgramsInCart" dbtype="query">
									select childPrograms
									from arguments.swRegCart
									where memberid = #int(val(arguments.memberID))#
									and SUBSTRING(item, 1, 3) = 'SWB'
									and LENGTH(childPrograms) > 0
								</cfquery>

								<cfset local.tmpStr.inCart = local.qryThisProgramInCart.recordCount gt 0 or listFindNoCase(valuelist(local.qryAllChildProgramsInCart.childPrograms), 'SWL-#int(val(local.qryPrograms.programID))#')>
							</cfif>

							<cfif arguments.depomemberdataID gt 0 AND local.strEnrollments.qrySWL.recordCount AND val(local.qryPrograms.enrollmentID) gt 0>
								<cfquery name="local.qryThisEnrolleeAttended" dbtype="query">
									select attended
									from [local].strEnrollments.qrySWL
									where enrollmentID = #int(val(local.qryPrograms.enrollmentID))#
								</cfquery>
								<cfset local.tmpStr.attended = local.qryThisEnrolleeAttended.attended is 1>
							</cfif>
						</cfif>

						<cfif arguments.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.qryPrograms.featureImageID) AND fileExists("#local.programFtdThumbImgFullRootPath##local.qryPrograms.featureImageID#-#local.qryPrograms.featureImageSizeID#.#local.qryPrograms.featureImageFileExtension#")>
							<cfset local.tmpStr.featuredImagePath = "#local.programFtdThumbImgRootPath##local.qryPrograms.featureImageID#-#local.qryPrograms.featureImageSizeID#.#local.qryPrograms.featureImageFileExtension#">
						<cfelseif len(local.defaultSWLFeaturedImagePath)>
							<cfset local.tmpStr.featuredImagePath = local.defaultSWLFeaturedImagePath>
						</cfif>

						<cfset local.strReturn.arrPrograms.append(local.tmpStr)>
					</cfcase>
					<cfcase value="SWOD">
						<cfset local.tmpStr = { "ft":local.qryPrograms.ft, "programID":local.qryPrograms.programID, "programTitle":local.qryPrograms.programName,
												"programSubTitle":local.qryPrograms.programSubTitle, "isRegistered":local.qryPrograms.isRegistered, 
												"freeRateDisplay":local.qryPrograms.freeRateDisplay, "isFeatured":local.qryPrograms.isFeatured, "arrLearningObjectives":[], 
												"arrRates":[], "dspCredits":"", "inCart":0, "passed":0, "uptoCreditsCount":0, "featuredImagePath":"", "incBundlesList":[],
												"isSaved":local.qryPrograms.isSaved, "videoPreviewLink":"", "videoPreviewDisplayName":"", "programBrand":arguments.qrySWP.brandSWODTab, "allowRegistrants":local.qryPrograms.allowRegistrants
											}>

						<cfif arguments.mode eq 'browse'>
							<cfif local.qryProgramLearningObjectives.recordCount>
								<cfquery name="local.tmpStr.arrLearningObjectives" dbtype="query" returntype="array">
									select objective
									from [local].qryProgramLearningObjectives
									where programID = #int(val(local.qryPrograms.programID))#
									and isSeminar = 1
									order by objectiveOrder
								</cfquery>
							</cfif>

							<cfif local.qryProgramRates.recordCount>
								<cfquery name="local.tmpStr.arrRates" dbtype="query" returntype="array">
									select price, description
									from [local].qryProgramRates
									where programID = #int(val(local.qryPrograms.programID))#
									and isSeminar = 1
									order by rowNum
								</cfquery>
							</cfif>

							<cfif local.qryProgramCredits.recordCount>
								<cfquery name="local.qryThisProgramCredits" dbtype="query">
									select authorityCode, creditType, max(creditValueAwarded) as creditValueAwarded
									from local.qryProgramCredits
									where programID = #int(val(local.qryPrograms.programID))#
									group by authorityCode, creditType
									order by authorityCode, creditType
								</cfquery>
								
								<cfif local.qryThisProgramCredits.recordCount>
									<cfquery name="local.qryUptoCreditCounts" dbtype="query">
										select authorityCode, sum(creditValueAwarded) as uptoCredits
										from local.qryThisProgramCredits
										group by authorityCode
										order by uptoCredits desc
									</cfquery>
									<cfset local.tmpStr.uptoCreditsCount = INT(val(local.qryUptoCreditCounts.uptoCredits))>
								
									<cfif local.qryUptoCreditCounts.recordCount is 1>
										<cfset local.tmpStr.dspCredits = "<span class='offeredCred'>Credits Offered: </span>">
										<cfloop query="local.qryThisProgramCredits">
											<cfset local.tmpStr.dspCredits = local.tmpStr.dspCredits & "#local.qryThisProgramCredits.creditValueAwarded# #local.qryThisProgramCredits.creditType#, ">
										</cfloop>
										<cfset local.tmpStr.dspCredits = mid(local.tmpStr.dspCredits, 1, len(local.tmpStr.dspCredits) - 2)>
									<cfelse>
										<cfset local.tmpStr.dspCredits = "Credit approved by <a href='{{creditsViewLink}}' class='{{classList}}'>#local.qryUptoCreditCounts.recordCount#</a> authorities">
									</cfif>
								</cfif>
							</cfif>

							<cfif local.qryProgramInBundles.recordCount>
								<cfquery name="local.qryIncBundles" dbtype="query">
									select distinct bundleName
									from [local].qryProgramInBundles
									where contentID = #int(val(local.qryPrograms.programID))#
									and contentType = 'SWOD'
									order by bundleName
								</cfquery>
								<cfset local.tmpStr.incBundlesList = jsStringFormat(valueList(local.qryIncBundles.bundleName,'<br />'))>
							</cfif>

							<cfif arguments.swRegCart.recordCount AND arguments.memberID gt 0>
								<cfquery name="local.qryThisProgramInCart" dbtype="query">
									select memberid
									from arguments.swRegCart
									where memberid = #int(val(arguments.memberID))#
									and item = 'SWOD-#int(val(local.qryPrograms.programID))#'
								</cfquery>

								<cfquery name="local.qryAllChildProgramsInCart" dbtype="query">
									select childPrograms
									from arguments.swRegCart
									where memberid = #int(val(arguments.memberID))#
									and SUBSTRING(item, 1, 3) = 'SWB'
									and LENGTH(childPrograms) > 0
								</cfquery>

								<cfset local.tmpStr.inCart = local.qryThisProgramInCart.recordCount gt 0 or listFindNoCase(valuelist(local.qryAllChildProgramsInCart.childPrograms), 'SWOD-#int(val(local.qryPrograms.programID))#')>
							</cfif>

							<cfif arguments.depomemberdataID gt 0 AND local.strEnrollments.qrySWOD.recordCount AND val(local.qryPrograms.enrollmentID) gt 0>
								<cfquery name="local.qryThisEnrolleeAttended" dbtype="query">
									select passed
									from [local].strEnrollments.qrySWOD
									where enrollmentID = #int(val(local.qryPrograms.enrollmentID))#
								</cfquery>
								<cfset local.tmpStr.passed = local.qryThisEnrolleeAttended.passed is 1>
							</cfif>

							<cfif local.qryProgramVideoPreviews.recordCount>
								<cfquery name="local.qryThisVideoPreview" dbtype="query">
									select fileID, fileName, fileTitle, participantID, participantOrgCode, videoPreviewFileName
									from [local].qryProgramVideoPreviews
									where programID = #int(val(local.qryPrograms.programID))#
								</cfquery>
								<cfif local.qryThisVideoPreview.recordCount>
									<cfset local.objectKey = lcase("swod/#local.qryThisVideoPreview.participantOrgCode#/#local.qryThisVideoPreview.participantID#/#local.qryThisVideoPreview.videoPreviewFileName#")>
									<cfset local.tmpStr.videoPreviewDisplayName = local.qryThisVideoPreview.fileTitle>
									<cfset local.tmpStr.videoPreviewLink = local.objSWFiles.getVideoPreviewLink(objectKey=local.objectKey, displayName="#local.qryThisVideoPreview.fileName#.mp4")>
								</cfif>
							</cfif>
						</cfif>

						<cfif arguments.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.qryPrograms.featureImageID) AND fileExists("#local.programFtdThumbImgFullRootPath##local.qryPrograms.featureImageID#-#local.qryPrograms.featureImageSizeID#.#local.qryPrograms.featureImageFileExtension#")>
							<cfset local.tmpStr.featuredImagePath = "#local.programFtdThumbImgRootPath##local.qryPrograms.featureImageID#-#local.qryPrograms.featureImageSizeID#.#local.qryPrograms.featureImageFileExtension#">
						<cfelseif len(local.defaultSWODFeaturedImagePath)>
							<cfset local.tmpStr.featuredImagePath = local.defaultSWODFeaturedImagePath>
						</cfif>

						<cfset local.strReturn.arrPrograms.append(local.tmpStr)>
					</cfcase>
					<cfcase value="EV">
						<cfstoredproc datasource="#application.dsn.membercentral.dsn#" procedure="ev_getRegistrationMetaByEventID">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#local.qryPrograms.programID#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="1">
							<cfprocresult name="local.qryRegMeta">
						</cfstoredproc>
						<cfstoredproc procedure="ev_getMerchantProfilesByRegistrationID" datasource="#application.dsn.membercentral.dsn#">
							<cfprocparam type="In" cfsqltype="CF_SQL_INTEGER" value="#val(local.qryRegMeta.registrationid)#">
							<cfprocresult name="local.qryCurrentProfiles">
						</cfstoredproc>
						<cfset local.merchantProfileCheck = 0>
						<cfif local.qryCurrentProfiles.recordCount>
							<cfset local.merchantProfileCheck = 1>	
						</cfif>
						<cfset local.tmpStr = { "ft":local.qryPrograms.ft, "programID":local.qryPrograms.programID, "programTitle":local.qryPrograms.programName,
												"programSubTitle":local.qryPrograms.programSubTitle, "programLocation":local.qryPrograms.programLocation,
												"displayStartTime":local.qryPrograms.displayStartTime, "displayEndTime":local.qryPrograms.displayEndTime, 
												"displayTimeZoneID":local.qryPrograms.displayTimeZoneID, "displayTimeZoneCode":local.qryPrograms.displayTimeZoneCode, 
												"displayTimeZoneAbbr":local.qryPrograms.displayTimeZoneAbbr, "isRegistered":local.qryPrograms.isRegistered, 
												"arrRates":[], "inCart":0, "featuredImagePath":"", "isFeatured":local.qryPrograms.isFeatured, "isSaved":local.qryPrograms.isSaved, 
												"displayedCurrencyType":"", "eventDetailLink":"", "programBrand":arguments.qrySWP.brandConfTab,
												"allowRegister":0, "showRates": 0, "altRegistrationURL": local.qryPrograms.altRegistrationURL , "eventRegV2Link":""
												}>

						<cfif arguments.mode eq 'browse'>
							<cfif len(local.qryPrograms.altRegistrationURL)>
								<cfset local.tmpStr.allowRegister = 1>								
							<cfelseif local.qryRegMeta.recordcount is 0>
							<cfelseif local.qryRegMeta.status neq "A">								
							<cfelseif now() lt local.qryRegMeta.startdate>							
							<cfelseif now() gt local.qryRegMeta.enddate>							
							<cfelseif local.qryRegMeta.regCapReached>							
							<cfelseif NOT local.merchantProfileCheck AND local.qryRegMeta.registrationTypeID EQ 1>								
							<cfelseif local.qryRegMeta.registrationType eq "RSVP">
								<cfset local.tmpStr.allowRegister = 1>								
							<cfelse>
								<cfset local.tmpStr.allowRegister = 1>								
								<cfset local.tmpStr.showRates = 1>								
							</cfif>

							<cfset local.tmpStr.displayedCurrencyType = local.displayedCurrencyType>

							<cfif local.qryEventRates.recordCount>
								<cfquery name="local.tmpStr.arrRates" dbtype="query" returntype="array">
									select rateName, rate, freeRateDisplay
									from [local].qryEventRates
									where eventID = #int(val(local.qryPrograms.programID))#
									order by rowNum
								</cfquery>
							</cfif>

							<cfif local.evRegCart.recordCount AND arguments.memberID gt 0>
								<cfquery name="local.qryThisProgramInCart" dbtype="query">
									select memberid
									from local.evRegCart
									where memberid = #int(val(arguments.memberID))#
									and eventID = #int(val(local.qryPrograms.programID))#
								</cfquery>
								<cfset local.tmpStr.inCart = local.qryThisProgramInCart.recordCount gt 0>
							</cfif>
						</cfif>

						<cfif arguments.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.qryPrograms.featureImageID) AND fileExists("#local.programFtdThumbImgFullRootPath##local.qryPrograms.featureImageID#-#local.qryPrograms.featureImageSizeID#.#local.qryPrograms.featureImageFileExtension#")>
							<cfset local.tmpStr.featuredImagePath = "#local.programFtdThumbImgRootPath##local.qryPrograms.featureImageID#-#local.qryPrograms.featureImageSizeID#.#local.qryPrograms.featureImageFileExtension#">
						<cfelseif len(local.defaultEVFeaturedImagePath)>
							<cfset local.tmpStr.featuredImagePath = local.defaultEVFeaturedImagePath>
						</cfif>

						<cfset var thisEventID = local.qryPrograms.programID>
						<cfset local.qryThisEventCalendar = QueryFilter(local.qryEventCalendars, 
																			function(thisRow) {
																				return arguments.thisRow.eventID eq thisEventID;	
																			})>
						
						<cfset local.baseEventProgramLink = application.objApplications.getAppBaseLink(applicationInstanceID=local.qryThisEventCalendar.calendarApplicationInstanceID, siteID=arguments.siteID)>
						<cfset local.tmpStr.eventDetailLink = "/?#local.baseEventProgramLink#&evAction=showDetail&eid=#thisEventID#">
						<cfset local.tmpStr.eventRegV2Link = "/?#local.baseEventProgramLink#&evAction=regV2&eid=#thisEventID#">

						<cfset local.strReturn.arrPrograms.append(local.tmpStr)>
					</cfcase>
					<cfcase value="SWB">
						<cfset local.tmpStr = { "ft":local.qryPrograms.ft, "programID":local.qryPrograms.programID, "programTitle":local.qryPrograms.programName,
												"programSubTitle":local.qryPrograms.programSubTitle, "programDesc":local.qryPrograms.programDesc,
												"isRegistered":local.qryPrograms.isRegistered, "freeRateDisplay":local.qryPrograms.freeRateDisplay,
												"isFeatured":local.qryPrograms.isFeatured, "arrLearningObjectives":[], "arrRates":[], 
												"arrIncPrograms":[], "inCart":0, "featuredImagePath":"", "isSaved":local.qryPrograms.isSaved,
												"dspStartDate":local.qryPrograms.dateStart, "dspEndDate":local.qryPrograms.dateEnd, "isSWOD":val(local.qryPrograms.isSWODBundle),
												"programBrand":(val(local.qryPrograms.isSWODBundle) ? arguments.qrySWP.brandBundleSWODTab : arguments.qrySWP.brandBundleSWLTab)
											}>

						<cfif arguments.mode eq 'browse'>
							<cfif local.qryProgramLearningObjectives.recordCount>
								<cfquery name="local.tmpStr.arrLearningObjectives" dbtype="query" returntype="array">
									select objective
									from [local].qryProgramLearningObjectives
									where programID = #int(val(local.qryPrograms.programID))#
									and isBundle = 1
									order by objectiveOrder
								</cfquery>
							</cfif>

							<cfif local.qryProgramRates.recordCount>
								<cfquery name="local.tmpStr.arrRates" dbtype="query" returntype="array">
									select price, description
									from [local].qryProgramRates
									where programID = #int(val(local.qryPrograms.programID))#
									and isBundle = 1
									order by rowNum
								</cfquery>
							</cfif>

							<cfif local.qryBundleItems.recordCount>
								<cfquery name="local.tmpStr.arrIncPrograms" dbtype="query" returntype="array">
									select contentID, contentName, dateStart
									from [local].qryBundleItems
									where bundleID = #int(val(local.qryPrograms.programID))#
									order by itemOrder
								</cfquery>
							</cfif>

							<cfif arguments.swRegCart.recordCount AND arguments.memberID gt 0>
								<cfquery name="local.qryThisProgramInCart" dbtype="query">
									select memberid
									from arguments.swRegCart
									where memberid = #int(val(arguments.memberID))#
									and item = 'SWB-#int(val(local.qryPrograms.programID))#'
								</cfquery>
								<cfset local.tmpStr.inCart = local.qryThisProgramInCart.recordCount gt 0>
							</cfif>
						</cfif>

						<cfif arguments.strAssociation.qryAssociation.hasSWProgramImageConfiguration AND val(local.qryPrograms.featureImageID) AND fileExists("#local.programFtdThumbImgFullRootPath##local.qryPrograms.featureImageID#-#local.qryPrograms.featureImageSizeID#.#local.qryPrograms.featureImageFileExtension#")>
							<cfset local.tmpStr.featuredImagePath = "#local.programFtdThumbImgRootPath##local.qryPrograms.featureImageID#-#local.qryPrograms.featureImageSizeID#.#local.qryPrograms.featureImageFileExtension#">
						<cfelseif len(local.defaultSWBFeaturedImagePath)>
							<cfset local.tmpStr.featuredImagePath = local.defaultSWBFeaturedImagePath>
						</cfif>

						<cfset local.strReturn.arrPrograms.append(local.tmpStr)>
					</cfcase>
				</cfswitch>
			</cfloop>
		</cfif>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="getCreditsInfoForBrowsePrograms" access="private" output="false" returntype="array">
		<cfargument name="catalogOrgCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.arrCreditsInfo = []>

		<cfset local.objSWL = CreateObject("component","model.seminarweb.SWLiveSeminars")>
		<cfset local.objSWOD = CreateObject("component","model.seminarweb.SWODSeminars")>

		<cfset local.qrySWLCreditAuthorities = local.objSWL.getCreditAuthoritiesForCatalog(catalogorgCode=arguments.catalogOrgCode)>
		<cfset local.qrySWLCreditsAvailable = local.objSWL.getCreditsAvailableForCatalogByAuthority(catalogorgCode=arguments.catalogOrgCode, authorityIDList=valuelist(local.qrySWLCreditAuthorities.authorityID))>
		<cfset local.qrySWODAuthorities = local.objSWOD.getCreditAuthoritiesForCatalog(catalogorgCode=arguments.catalogOrgCode)>
		<cfset local.qrySWODCreditsAvailable = local.objSWOD.getCreditsAvailableForCatalogByAuthority(catalogorgCode=arguments.catalogOrgCode, authorityIDList=valuelist(local.qrySWODAuthorities.authorityID))>

		<cfquery name="local.qrySWCreditAuthorities" dbtype="query">
			select authorityID, wddxCreditTypes
			from [local].qrySWLCreditAuthorities
				union
			select authorityID, wddxCreditTypes
			from [local].qrySWODAuthorities
		</cfquery>

		<cfquery name="local.qrySWProgramCreditAuthorities" dbtype="query">
			select authorityID, authorityName, code as authorityCode, creditTypesAvailableXml
			from [local].qrySWLCreditsAvailable
			where creditTypesAvailableXml <> ''
				union
			select authorityID, authorityName, code as authorityCode, creditTypesAvailableXml
			from [local].qrySWODCreditsAvailable
			where creditTypesAvailableXml <> ''
		</cfquery>

		<cfset local.qrySWProgramCreditAuthorities.sort('authorityName','asc')>

		<cfoutput query="local.qrySWProgramCreditAuthorities" group="authorityID">
			<cfset local.tmpStr = {
				"authorityID":local.qrySWProgramCreditAuthorities.authorityID,
				"authorityName":local.qrySWProgramCreditAuthorities.authorityName,
				"authorityCode":local.qrySWProgramCreditAuthorities.authorityCode,
				"strCreditTypes":{}
			}>

			<cfquery name="local.qryThisWDDXCreditTypes" dbtype="query">
				select wddxCreditTypes
				from [local].qrySWCreditAuthorities
				where authorityID = #val(local.qrySWProgramCreditAuthorities.authorityID)#
			</cfquery>
			
			<cfif len(local.qryThisWDDXCreditTypes.wddxCreditTypes)>
				<cfoutput>
					<cfset local.xmlCreditsAvailable = "<creditTypes>" & local.qrySWProgramCreditAuthorities.creditTypesAvailableXml & "</creditTypes>">
					<cfset local.arrThisCreditTypesAvailable = xmlSearch(xmlParse(local.xmlCreditsAvailable),'/creditTypes/string')>
					
					<cfloop array="#local.arrThisCreditTypesAvailable#" index="local.thisCreditType">
						<cfif NOT structKeyExists(local.tmpStr.strCreditTypes,"local.thisCreditType.XmlText")>
							<!--- Use XPath to get the proper description for credit type --->
							<cfset local.displayname = xmlsearch(local.qryThisWDDXCreditTypes.wddxcredittypes,'string(//struct/var[@name=''displayname'']/string[../../var[@name=''fieldname'' and translate(string,''ABCDEFGHIJKLMNOPQRSTUVWXYZ'',''abcdefghijklmnopqrstuvwxyz'')=''#lcase(local.thisCreditType.XmlText)#'']])')>
							<cfset local.tmpStr.strCreditTypes[local.thisCreditType.XmlText] = local.displayname>
						</cfif>
					</cfloop>
				</cfoutput>
			</cfif>

			<cfset local.arrCreditsInfo.append(local.tmpStr)>
		</cfoutput>

		<cfreturn local.arrCreditsInfo>
	</cffunction>

	<cffunction name="getAuthorsForBrowsePrograms" access="private" output="false" returntype="array">
		<cfargument name="catalogOrgCode" type="string" required="true">

		<cfset var local = structNew()>
		<cfset local.qryAuthors = createObject("component","model.seminarweb.SWAuthors").getAuthorsForCatalogSearch(catalogOrgCode=arguments.catalogOrgCode)>

		<cfquery name="local.arrAuthors" dbtype="query" returntype="array">
			select authorID, prefix, firstname, middlename, lastname, suffix
			from [local].qryAuthors
			order by prefix, firstname, middlename, lastname, suffix
		</cfquery>

		<cfset local.arrAuthors = local.arrAuthors.map(
			function(item,index) {
				local.authorName = "";
				if (len(arguments.item.prefix)) local.authorName &= "#arguments.item.prefix# ";
				local.authorName &= "#arguments.item.firstname# ";
				if (len(arguments.item.middlename)) local.authorName &= "#arguments.item.middlename# ";
				local.authorName &= "#arguments.item.lastname#";
				if (len(arguments.item.suffix)) local.authorName &= ", #arguments.item.suffix#";
				return {
					"authorID":arguments.item.authorID,
					"authorName":trim(local.authorName)
				};
			})>

		<cfreturn local.arrAuthors>
	</cffunction>

	<cffunction name="getSWProgramsSavedForLater" access="public" output="false" returntype="query">
		<cfset var qrySWPrograms = "">
				
		<cfif structKeyExists(cookie,"SWBROWSE") AND isValid('guid',cookie.SWBROWSE)>
			<cfstoredproc procedure="sw_getSWProgramsSavedForLater" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_IDSTAMP" value="#cookie.SWBROWSE#">
				<cfprocresult name="qrySWPrograms" resultset="1">
			</cfstoredproc>
		<cfelse>
			<cfset qrySWPrograms = QueryNew('')>
		</cfif>

		<cfreturn qrySWPrograms>
	</cffunction>

	<cffunction name="saveSWProgramForLater" access="public" output="false" returntype="struct">
		<cfargument name="programID" type="string" required="no" default="0" hint="Intentionally a string to prevent issues from invalid direct ajax requests">
		<cfargument name="programType" type="string" required="no" default="xx">

		<cfset var local = structNew()>

		<cftry>
			<cfif not isValid("integer",arguments.programID) OR arguments.programID LT 1 OR arguments.programID GT 100000 OR NOT listFindNoCase("swl,swod,swb,ev",arguments.programType)>
				<cfset local.strReturn = { "success":false }>
			<cfelse>
				<cfif structKeyExists(cookie,"SWBROWSE") AND isValid('guid',cookie.SWBROWSE)>
					<cfset local.savedKey = cookie.SWBROWSE>
				<cfelse>
					<cfset local.cookieExpires = dateadd("d",30,now())>
					<cfset local.savedKey = createGUID()>
					<cfheader name="Set-Cookie" value="SWBROWSE=#local.savedKey#; expires=#getHTTPTimeString(local.cookieExpires)#; HttpOnly;">
				</cfif>

				<cfstoredproc procedure="sw_saveProgramForLater" datasource="#application.dsn.tlasites_seminarweb.dsn#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_IDSTAMP" value="#local.savedKey#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
					<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">
				</cfstoredproc>

				<cfset local.strReturn = { "success":true }>
			</cfif>
		<cfcatch type="Any">
			<cfset local.strReturn = { "success":false }>
		</cfcatch>
		</cftry>

		<cfreturn local.strReturn>
	</cffunction>

	<cffunction name="removeSavedSWProgram" access="public" output="false" returntype="struct">
		<cfargument name="programID" type="numeric" required="yes">
		<cfargument name="programType" type="string" required="yes">
		
		<cfset var strReturn = {}>

		<cfif listFindNoCase("swl,swod,swb,ev",arguments.programType) AND structKeyExists(cookie,"SWBROWSE") AND isValid('guid',cookie.SWBROWSE)>
			<cfstoredproc procedure="sw_removeSavedSWProgram" datasource="#application.dsn.tlasites_seminarweb.dsn#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_IDSTAMP" value="#cookie.SWBROWSE#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.programID#">
				<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.programType#">
			</cfstoredproc>
			<cfset strReturn = { "success":true }>
		<cfelse>
			<cfset strReturn = { "success":false }>
		</cfif>

		<cfreturn strReturn>
	</cffunction>

	<cffunction name="getDefaultProgramFeaturedImagePaths" access="public" output="false" returntype="struct">
		<cfargument name="strAssociation" type="struct" required="true">
		<cfargument name="mode" type="string" required="true" hint="browse or featured or myFeatured">

		<cfset var local = structNew()>

		<cfset local.resultStr = {
			defaultSWLFeaturedImagePath = "",
			defaultSWODFeaturedImagePath = "",
			defaultSWBFeaturedImagePath = "",
			defaultEVFeaturedImagePath = ""
		}>

		<cfif arguments.strAssociation.qryAssociation.hasSWProgramImageConfiguration>
			<cfset local.qryPlatformFeaturedImageSetup = createObject("component","model.admin.common.modules.featuredImages.featuredImages").getPlatformFeaturedImagesSetup()>
			<cfset local.qryParticipantFeaturedImageSetup = arguments.strAssociation.qryParticipantFeaturedImageSetup>

			<cfset local.platformFtdThumbImgFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.qryPlatformFeaturedImageSetup.orgcode)#/#LCASE(local.qryPlatformFeaturedImageSetup.sitecode)#/featuredimages/thumbnails/">
			<cfset local.platformFtdThumbImgRootPath = "/userassets/#LCASE(local.qryPlatformFeaturedImageSetup.orgcode)#/#LCASE(local.qryPlatformFeaturedImageSetup.sitecode)#/featuredimages/thumbnails/">
			<cfset local.siteDefaultFtdThumbImgFullRootPath = "#application.paths.RAIDUserAssetRoot.path##LCASE(local.qryParticipantFeaturedImageSetup.orgcode)#/#LCASE(local.qryParticipantFeaturedImageSetup.sitecode)#/featuredimages/thumbnails/">
			<cfset local.siteDefaultFtdThumbImgRootPath = "/userassets/#LCASE(local.qryParticipantFeaturedImageSetup.orgcode)#/#LCASE(local.qryParticipantFeaturedImageSetup.sitecode)#/featuredimages/thumbnails/">

			<cfswitch expression="#arguments.mode#">
				<cfcase value="browse,mergecode">
					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#")>
						<cfset local.resultStr.defaultSWLFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#")>
						<cfset local.resultStr.defaultSWLFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#">
					</cfif>

					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#")>
						<cfset local.resultStr.defaultSWODFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#")>
						<cfset local.resultStr.defaultSWODFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#">
					</cfif>

					<cfif val(local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#")>
						<cfset local.resultStr.defaultEVFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#")>
						<cfset local.resultStr.defaultEVFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#">
					</cfif>

					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#")>
						<cfset local.resultStr.defaultSWBFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramListingsImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#")>
						<cfset local.resultStr.defaultSWBFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramListingsFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramListingsImageFileExtension#">
					</cfif>
				</cfcase>
				<cfcase value="detail">
					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWLFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramDetailImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWLFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramDetailImageFileExtension#">
					</cfif>

					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWODFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramDetailImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWODFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramDetailImageFileExtension#">
					</cfif>

					<cfif val(local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultEVFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramDetailImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultEVFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramDetailImageFileExtension#">
					</cfif>

					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWBFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swProgramDetailImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWBFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swProgramDetailImageFileExtension#">
					</cfif>
				</cfcase>
				<cfcase value="otherprograms">
					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWLFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWLFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailImageFileExtension#">
					</cfif>

					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWODFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWODFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailImageFileExtension#">
					</cfif>

					<cfif val(local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultEVFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultEVFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailImageFileExtension#">
					</cfif>

					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWBFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swOtherProgramDetailImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailImageFileExtension#")>
						<cfset local.resultStr.defaultSWBFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swOtherProgramDetailImageFileExtension#">
					</cfif>
				</cfcase>
				<cfcase value="featured,myFeatured">
					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#")>
						<cfset local.resultStr.defaultSWLFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#")>
						<cfset local.resultStr.defaultSWLFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWLProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#">
					</cfif>

					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#")>
						<cfset local.resultStr.defaultSWODFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#")>
						<cfset local.resultStr.defaultSWODFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWODProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#">
					</cfif>
					
					<cfif val(local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#")>
						<cfset local.resultStr.defaultEVFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#")>
						<cfset local.resultStr.defaultEVFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultMCEVProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#">
					</cfif>
		
					<cfif val(local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID) AND fileExists("#local.siteDefaultFtdThumbImgFullRootPath##local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#")>
						<cfset local.resultStr.defaultSWBFeaturedImagePath = "#local.siteDefaultFtdThumbImgRootPath##local.qryParticipantFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryParticipantFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#">
					<cfelseif val(local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID) AND fileExists("#local.platformFtdThumbImgFullRootPath##local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#")>
						<cfset local.resultStr.defaultSWBFeaturedImagePath = "#local.platformFtdThumbImgRootPath##local.qryPlatformFeaturedImageSetup.defaultSWBProgramFeatureImageID#-#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingFeatureImageSizeID#.#local.qryPlatformFeaturedImageSetup.swFeaturedProgramLandingImageFileExtension#">
					</cfif>
				</cfcase>
			</cfswitch>
		</cfif>

		<cfreturn local.resultStr>
	</cffunction>

	<cffunction name="getTrimmedProgramTitleDisplay" access="public" output="false" returntype="string">
		<cfargument name="programTitle" type="string" required="yes">
		<cfargument name="programSubTitle" type="string" required="yes">

		<cfset var local = structNew()>

		<cfif len(arguments.programTitle) lte 70 and len(arguments.programSubTitle)>
			<cfset local.programTitleFull = arguments.programTitle & ": " & arguments.programSubTitle>
		<cfelse>
			<cfset local.programTitleFull = arguments.programTitle>
		</cfif>
		<cfset local.programTitleTrimmed = left(local.programTitleFull, 75)>
		<cfif len(local.programTitleFull) gt 75>
			<cfset local.programTitleTrimmed &= "...">
		</cfif>

		<cfreturn local.programTitleTrimmed>
	</cffunction>

	<cffunction name="getSWProgramIDsAvailableForBrowse" access="public" output="false" returntype="struct">
		<cfargument name="catalogOrgCode" type="string" required="true">
		<cfargument name="memberID" type="numeric" required="false" default="0">

		<cfset var local = structNew()>
		<cfset local.resultStr = { "arrSWL":[], "arrSWOD":[], "arrEvents":[] }>

		<cfstoredproc procedure="sw_getProgramIDsAvailableForBrowse" datasource="#application.dsn.tlasites_seminarweb.dsn#">			
			<cfprocparam type="IN" cfsqltype="CF_SQL_VARCHAR" value="#arguments.catalogOrgCode#">
			<cfprocparam type="IN" cfsqltype="CF_SQL_INTEGER" value="#arguments.memberID#">
			<cfprocresult name="local.qryPrograms" resultset="1">
		</cfstoredproc>

		<cfquery name="local.qrySWL" dbtype="query">
			SELECT programID FROM [local].qryPrograms WHERE programType = 'SWL';
		</cfquery>

		<cfquery name="local.qrySWOD" dbtype="query">
			SELECT programID FROM [local].qryPrograms WHERE programType = 'SWOD';
		</cfquery>

		<cfquery name="local.qryEV" dbtype="query">
			SELECT programID FROM [local].qryPrograms WHERE programType = 'EV';
		</cfquery>

		<cfset local.resultStr.arrSWLProgramIDs = ValueArray(local.qrySWL,"programID")>
		<cfset local.resultStr.arrSWODProgramIDs = ValueArray(local.qrySWOD,"programID")>
		<cfset local.resultStr.arrEvProgramIDs = ValueArray(local.qryEV,"programID")>

		<cfreturn local.resultStr>
	</cffunction>

	<cffunction name="toggleSWProgramFilterMenu" access="public" output="false" returntype="struct">
		<cfargument name="isClosed" type="string" required="no" default="0" hint="Intentionally a string to protect against invalid ajax calls">

		<cfset var local = structNew()>
		<cfset local.returnVal = true>

		<cfif NOT isValid("boolean",arguments.isClosed)>
			<cfset local.returnVal = false>
		<cfelse>
			<cfset local.swCatalogFilterMenuIsClosed = arguments.isClosed ? true : false>
			<cfset application.mcCacheManager.sessionSetValue(keyname='swCatalogFilterMenuIsClosed', value=local.swCatalogFilterMenuIsClosed)>
		</cfif>

		<cfreturn { "success":local.returnVal }>
	</cffunction>

	<cffunction name="hasActiveBundles" access="public" output="false" returntype="boolean">
		<cfargument name="catalogOrgCode" type="string" required="yes">

		<cfset var qryHasActiveBundles = "">

		<cfquery name="qryHasActiveBundles" datasource="#application.dsn.tlasites_seminarweb.dsn#">
			SET NOCOUNT ON;
			SET TRANSACTION ISOLATION LEVEL SNAPSHOT;

			DECLARE @participantID int;
			SET @participantID = dbo.fn_getParticipantIDFromOrgcode(<cfqueryparam cfsqltype="CF_SQL_VARCHAR" value="#arguments.catalogOrgCode#">);

			SELECT TOP 1 b.bundleID
			FROM dbo.tblBundles as b
			WHERE b.participantID = @participantID
			AND b.status = 'A'
				UNION
			SELECT TOP 1 b.bundleID
			FROM dbo.tblBundles as b
			INNER JOIN dbo.tblBundlesOptIn as boi on boi.bundleID = b.bundleID AND boi.isActive = 1 and boi.sellCatalog = 1
			WHERE boi.participantID = @participantID
			AND b.status = 'A'
			AND b.allowSyndication = 1;

			SET TRANSACTION ISOLATION LEVEL READ COMMITTED;
		</cfquery>

		<cfreturn qryHasActiveBundles.recordCount GT 0>
	</cffunction>

</cfcomponent>